#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试采集功能
"""

import requests
import time
from bs4 import BeautifulSoup
import urllib.parse

def quick_search_test():
    """快速搜索测试"""
    try:
        print("🔍 快速搜索测试...")
        
        # 代理设置
        proxy_settings = {
            "http": "http://u1856561711670614:<EMAIL>:36931",
            "https": "http://u1856561711670614:<EMAIL>:36931"
        }
        
        # 创建会话
        session = requests.Session()
        session.proxies = proxy_settings
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        # 测试关键词
        keyword = "fan"
        search_query = f"amazon.com {keyword}"
        encoded_query = urllib.parse.quote_plus(search_query)
        search_url = f"https://www.bing.com/search?q={encoded_query}"
        
        print(f"关键词: {keyword}")
        print(f"搜索查询: {search_query}")
        print(f"搜索URL: {search_url}")
        
        # 发送请求
        print("发送搜索请求...")
        start_time = time.time()
        
        response = session.get(search_url, timeout=(15, 45))
        
        end_time = time.time()
        print(f"请求完成，耗时: {end_time - start_time:.2f} 秒")
        print(f"状态码: {response.status_code}")
        print(f"响应大小: {len(response.text)} 字符")
        
        if response.status_code == 200:
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            amazon_codes = []
            
            # 查找所有包含Amazon链接的元素
            links = soup.find_all('a', href=True)
            print(f"找到 {len(links)} 个链接")
            
            amazon_link_count = 0
            for link in links:
                href = link.get('href', '')
                
                # 检查是否是Amazon链接
                if 'amazon.' in href:
                    amazon_link_count += 1
                    
                    if '/dp/' in href:
                        # 提取ASIN代码
                        import re
                        asin_match = re.search(r'/dp/([A-Z0-9]{10})', href)
                        if asin_match:
                            asin = asin_match.group(1)
                            if asin not in amazon_codes:
                                amazon_codes.append(asin)
                                
                    elif '/gp/product/' in href:
                        asin_match = re.search(r'/gp/product/([A-Z0-9]{10})', href)
                        if asin_match:
                            asin = asin_match.group(1)
                            if asin not in amazon_codes:
                                amazon_codes.append(asin)
            
            print(f"找到 {amazon_link_count} 个Amazon链接")
            print(f"提取到 {len(amazon_codes)} 个产品代码")
            
            if amazon_codes:
                print("产品代码:")
                for i, code in enumerate(amazon_codes[:10], 1):
                    print(f"  {i}. {code}")
                if len(amazon_codes) > 10:
                    print(f"  ... 还有 {len(amazon_codes) - 10} 个")
                return True
            else:
                print("未找到产品代码")
                
                # 保存HTML用于调试
                with open('debug_response.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print("已保存响应到 debug_response.html")
                return False
        else:
            print(f"请求失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return False

def test_without_proxy():
    """无代理测试"""
    try:
        print("\n🌐 无代理测试...")
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        keyword = "fan"
        search_query = f"amazon.com {keyword}"
        encoded_query = urllib.parse.quote_plus(search_query)
        search_url = f"https://www.bing.com/search?q={encoded_query}"
        
        print("发送无代理请求...")
        response = session.get(search_url, timeout=30)
        
        print(f"状态码: {response.status_code}")
        print(f"响应大小: {len(response.text)} 字符")
        
        if response.status_code == 200:
            if 'amazon.com' in response.text.lower():
                print("✅ 响应中包含Amazon链接")
                return True
            else:
                print("⚠️ 响应中未找到Amazon链接")
                return False
        else:
            return False
            
    except Exception as e:
        print(f"无代理测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 快速搜索测试")
    print("=" * 50)
    
    # 测试有代理
    proxy_ok = quick_search_test()
    
    # 测试无代理
    no_proxy_ok = test_without_proxy()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   代理搜索: {'✅ 成功' if proxy_ok else '❌ 失败'}")
    print(f"   无代理搜索: {'✅ 成功' if no_proxy_ok else '❌ 失败'}")
    
    if proxy_ok:
        print("\n🎉 代理搜索正常！")
    elif no_proxy_ok:
        print("\n⚠️ 无代理搜索正常，但代理搜索有问题。")
    else:
        print("\n❌ 搜索功能异常。")

if __name__ == "__main__":
    main()
