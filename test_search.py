#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试采集.py的搜索功能
"""

import sys
import os
import tkinter as tk

def test_search_functionality():
    """测试搜索功能"""
    try:
        print("🔍 测试搜索功能...")
        
        # 导入采集模块
        sys.path.insert(0, os.getcwd())
        from 采集 import AmazonScraper
        
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        # 创建AmazonScraper实例
        scraper = AmazonScraper(root)
        print("✅ AmazonScraper实例创建成功")
        
        # 测试代理连接
        print("\n🔗 测试代理连接...")
        proxy_result = scraper.test_proxy_connection()
        
        if proxy_result:
            print("✅ 代理连接测试通过")
        else:
            print("⚠️ 代理连接测试失败，但可以继续测试搜索功能")
        
        # 测试搜索功能
        print("\n🔍 测试搜索功能...")
        test_keyword = "fan"
        
        # 获取搜索查询格式
        country_info = scraper.countries["美国"]
        search_query = country_info["query_format"].format(keyword=test_keyword)
        print(f"搜索查询: {search_query}")
        
        # 执行搜索
        print("开始搜索...")
        amazon_codes = scraper.search_with_requests(search_query, test_keyword)
        
        if amazon_codes:
            print(f"✅ 搜索成功！找到 {len(amazon_codes)} 个Amazon产品代码:")
            for i, code in enumerate(amazon_codes[:5], 1):  # 只显示前5个
                print(f"   {i}. {code}")
            if len(amazon_codes) > 5:
                print(f"   ... 还有 {len(amazon_codes) - 5} 个")
        else:
            print("⚠️ 搜索完成，但未找到Amazon产品代码")
            print("这可能是因为:")
            print("   1. 代理连接问题")
            print("   2. 搜索结果中没有Amazon链接")
            print("   3. HTML解析问题")
        
        # 测试HTML解析功能
        print("\n🔧 测试HTML解析功能...")
        test_html = '''
        <html>
        <body>
        <a href="https://www.amazon.com/dp/B08N5WRWNW">Test Product 1</a>
        <a href="https://amazon.com/gp/product/B07XJ8C8F7">Test Product 2</a>
        <a href="https://www.google.com">Not Amazon</a>
        </body>
        </html>
        '''
        
        test_codes = scraper.extract_amazon_codes_from_html(test_html, "test")
        if test_codes:
            print(f"✅ HTML解析功能正常，提取到 {len(test_codes)} 个代码: {test_codes}")
        else:
            print("❌ HTML解析功能异常")
        
        # 清理
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        return False

def test_direct_request():
    """直接测试HTTP请求"""
    try:
        print("\n🌐 测试直接HTTP请求...")
        import requests
        
        # 测试无代理请求
        print("测试无代理请求...")
        response = requests.get("https://httpbin.org/ip", timeout=10)
        if response.status_code == 200:
            ip_info = response.json()
            print(f"✅ 无代理请求成功，IP: {ip_info.get('origin')}")
        
        # 测试代理请求
        print("测试代理请求...")
        proxy_settings = {
            "http": "http://u1856561711670614:<EMAIL>:36931",
            "https": "http://u1856561711670614:<EMAIL>:36931"
        }
        
        response = requests.get("https://httpbin.org/ip", proxies=proxy_settings, timeout=15)
        if response.status_code == 200:
            ip_info = response.json()
            print(f"✅ 代理请求成功，IP: {ip_info.get('origin')}")
        else:
            print(f"⚠️ 代理请求失败，状态码: {response.status_code}")
            
        # 测试搜索引擎请求
        print("测试搜索引擎请求...")
        search_url = "https://www.bing.com/search?q=amazon+fan"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(search_url, headers=headers, proxies=proxy_settings, timeout=20)
        if response.status_code == 200:
            print(f"✅ 搜索引擎请求成功，响应大小: {len(response.text)} 字符")
            
            # 简单检查是否包含Amazon链接
            if 'amazon.com' in response.text.lower():
                print("✅ 响应中包含Amazon链接")
            else:
                print("⚠️ 响应中未找到Amazon链接")
        else:
            print(f"❌ 搜索引擎请求失败，状态码: {response.status_code}")
            
        return True
        
    except Exception as e:
        print(f"❌ 直接请求测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试采集.py的搜索功能")
    print("=" * 60)
    
    # 测试直接请求
    direct_ok = test_direct_request()
    
    # 测试搜索功能
    search_ok = test_search_functionality()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   直接请求测试: {'✅ 通过' if direct_ok else '❌ 失败'}")
    print(f"   搜索功能测试: {'✅ 通过' if search_ok else '❌ 失败'}")
    
    if direct_ok and search_ok:
        print("\n🎉 所有测试通过！搜索功能正常。")
    elif direct_ok:
        print("\n⚠️ 直接请求正常，但搜索功能有问题。")
        print("建议检查:")
        print("   1. BeautifulSoup是否正确安装")
        print("   2. HTML解析逻辑是否正确")
    else:
        print("\n❌ 网络连接或代理有问题。")
        print("建议检查:")
        print("   1. 网络连接是否正常")
        print("   2. 代理配置是否正确")
        print("   3. 防火墙设置")
    
    return direct_ok and search_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
