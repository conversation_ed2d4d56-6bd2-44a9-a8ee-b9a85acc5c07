# 智能代理选择系统使用说明

## 概述

智能代理选择系统是一个基于成功率的自适应代理选择机制，能够：

1. **自动选择最佳代理**：根据历史成功率智能选择代理服务器
2. **动态权重调整**：成功率高的代理被选中的概率更大
3. **性能统计分析**：记录每个代理的成功率、响应时间等指标
4. **持久化存储**：统计数据保存到文件，重启后继续使用历史数据

## 代理服务器配置

系统配置了5个123Proxy代理服务器：

- **服务器1**: proxy.123proxy.cn:36931
- **服务器2**: proxy.123proxy.cn:36930  
- **服务器3**: proxy.123proxy.cn:36927
- **服务器4**: proxy.123proxy.cn:36928
- **服务器5**: proxy.123proxy.cn:36929

## 核心功能

### 1. 智能代理选择算法

```python
def select_proxy(self):
    """基于权重随机选择代理"""
    weights = self.get_proxy_weights()
    
    # 加权随机选择
    total_weight = sum(weights)
    rand = random.uniform(0, total_weight)
    
    # 找到对应的代理
    current_weight = 0
    for i, weight in enumerate(weights):
        current_weight += weight
        if rand <= current_weight:
            return self.proxy_servers[i]
```

### 2. 权重计算规则

- **新代理**：如果尝试次数 < 10次，权重 = 1.0（给予更多机会）
- **有历史数据的代理**：权重 = max(0.1, 成功率)（最低权重0.1，避免完全排除）

### 3. 统计数据记录

每次请求后记录：
- 成功/失败次数
- 平均响应时间
- 总请求次数
- 成功率计算

## 使用方法

### 1. 在品牌.py中使用

智能代理选择系统已经集成到品牌.py中：

```python
# 系统会自动使用智能代理选择
scraper = AmazonScraper()
# 系统会根据成功率自动选择最佳代理
```

### 2. 独立测试

运行测试脚本：

```bash
python 测试智能代理选择.py
```

### 3. 查看统计信息

```python
# 在品牌.py中
scraper.print_proxy_stats()  # 显示统计信息
scraper.save_proxy_stats()   # 保存统计数据
```

## 统计信息说明

运行时会显示如下统计表格：

```
=== 代理服务器统计信息 ===
代理名称     成功次数   失败次数   总次数    成功率    平均响应时间   权重    
--------------------------------------------------------------------------------
服务器1      15        2         17       88.24%   2.34s        0.88    
服务器2      12        5         17       70.59%   3.12s        0.71    
服务器3      8         9         17       47.06%   4.56s        0.47    
服务器4      14        3         17       82.35%   2.89s        0.82    
服务器5      10        7         17       58.82%   3.78s        0.59    
```

### 字段说明

- **代理名称**：代理服务器标识
- **成功次数**：请求成功的次数
- **失败次数**：请求失败的次数  
- **总次数**：总请求次数
- **成功率**：成功次数/总次数
- **平均响应时间**：成功请求的平均响应时间
- **权重**：当前选择权重（权重越高，被选中概率越大）

## 优势特点

### 1. 自适应选择
- 根据实际使用效果动态调整代理选择策略
- 成功率高的代理自动获得更高优先级

### 2. 负载均衡
- 避免所有请求集中在单一代理上
- 即使最差的代理也保持最低10%的选择概率

### 3. 探索与利用平衡
- 新代理或使用次数少的代理获得探索机会
- 已验证的高质量代理获得更多使用机会

### 4. 数据持久化
- 统计数据保存到`proxy_stats.json`文件
- 程序重启后继续使用历史统计数据

## 配置选项

### 修改代理服务器

在`SmartProxyManager`类中修改`proxy_servers`列表：

```python
self.proxy_servers = [
    {
        "name": "新服务器",
        "http": "http://用户名:密码@主机:端口",
        "https": "http://用户名:密码@主机:端口"
    },
    # 添加更多服务器...
]
```

### 调整参数

```python
self.min_attempts = 10  # 最小尝试次数阈值
```

## 文件说明

- **品牌.py**：主程序，已集成智能代理选择系统
- **测试智能代理选择.py**：独立测试脚本
- **proxy_stats.json**：统计数据存储文件（自动生成）
- **智能代理选择系统说明.md**：本说明文档

## 注意事项

1. **首次运行**：新代理没有历史数据时，会平均分配选择概率
2. **数据积累**：使用一段时间后，系统会根据实际表现优化选择策略
3. **定期清理**：可以删除`proxy_stats.json`文件重置统计数据
4. **网络环境**：代理性能可能受网络环境影响，建议定期观察统计数据

## 故障排除

### 1. 所有代理都失败
- 检查代理服务器配置是否正确
- 确认代理账户是否有足够流量
- 检查网络连接是否正常

### 2. 统计数据异常
- 删除`proxy_stats.json`文件重新开始统计
- 检查文件权限是否允许读写

### 3. 选择偏向性过强
- 适当增加`min_attempts`值给新代理更多机会
- 调整权重计算公式中的最小权重值

通过智能代理选择系统，您可以获得更稳定、高效的代理使用体验！
