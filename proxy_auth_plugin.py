import os
import zipfile
import shutil
import tempfile

def create_proxy_auth_extension(proxy_host, proxy_port, proxy_username, proxy_password):
    """
    创建Chrome浏览器的代理认证插件
    
    Args:
        proxy_host (str): 代理服务器主机名或IP
        proxy_port (str): 代理服务器端口
        proxy_username (str): 代理认证用户名
        proxy_password (str): 代理认证密码
        
    Returns:
        str: 创建的扩展文件路径
    """
    # 创建一个临时目录来存储扩展文件
    temp_dir = tempfile.mkdtemp()
    
    # 创建manifest.json文件
    manifest_json = """
    {
        "version": "1.0.0",
        "manifest_version": 2,
        "name": "Chrome Proxy",
        "permissions": [
            "proxy",
            "tabs",
            "unlimitedStorage",
            "storage",
            "webRequest",
            "webRequestBlocking"
        ],
        "background": {
            "scripts": ["background.js"]
        },
        "minimum_chrome_version": "22.0.0"
    }
    """
    
    # 创建background.js文件
    background_js = """
    var config = {
        mode: "fixed_servers",
        rules: {
            singleProxy: {
                scheme: "http",
                host: "%s",
                port: parseInt(%s)
            },
            bypassList: ["localhost"]
        }
    };

    chrome.proxy.settings.set({value: config, scope: "regular"}, function() {});

    function callbackFn(details) {
        return {
            authCredentials: {
                username: "%s",
                password: "%s"
            }
        };
    }

    chrome.webRequest.onAuthRequired.addListener(
        callbackFn,
        {urls: ["<all_urls>"]},
        ['blocking']
    );
    """ % (proxy_host, proxy_port, proxy_username, proxy_password)
    
    # 写入文件
    with open(os.path.join(temp_dir, "manifest.json"), "w") as f:
        f.write(manifest_json.strip())
    with open(os.path.join(temp_dir, "background.js"), "w") as f:
        f.write(background_js.strip())
    
    # 打包成.zip文件
    extension_file = tempfile.NamedTemporaryFile(suffix='.zip', delete=False).name
    with zipfile.ZipFile(extension_file, 'w') as zp:
        for root, _, files in os.walk(temp_dir):
            for file in files:
                zp.write(os.path.join(root, file), os.path.relpath(os.path.join(root, file), temp_dir))
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    return extension_file 