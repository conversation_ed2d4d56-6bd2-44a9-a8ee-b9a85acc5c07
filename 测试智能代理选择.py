#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能代理选择系统测试脚本

这个脚本演示了如何使用智能代理选择系统：
1. 自动选择最佳代理服务器
2. 根据成功率动态调整选择概率
3. 记录和分析代理性能统计
"""

import requests
import time
import random
from collections import defaultdict
import json
import os

class SmartProxyManager:
    def __init__(self):
        # 定义所有代理服务器
        self.proxy_servers = [
            {
                "name": "服务器1",
                "http": "http://u1856561711690016:<EMAIL>:36931",
                "https": "http://u1856561711690016:<EMAIL>:36931"
            },
            {
                "name": "服务器2",
                "http": "http://u1856561711690016:<EMAIL>:36930",
                "https": "http://u1856561711690016:<EMAIL>:36930"
            },
            {
                "name": "服务器3",
                "http": "http://u1856561711690016:<EMAIL>:36927",
                "https": "http://u1856561711690016:<EMAIL>:36927"
            },
            {
                "name": "服务器4",
                "http": "http://u1856561711690016:<EMAIL>:36928",
                "https": "http://u1856561711690016:<EMAIL>:36928"
            },
            {
                "name": "服务器5",
                "http": "http://u1856561711690016:<EMAIL>:36929",
                "https": "http://u1856561711690016:<EMAIL>:36929"
            }
        ]
        
        # 统计数据
        self.stats = defaultdict(lambda: {"success": 0, "failure": 0, "total": 0, "avg_response_time": 0})
        self.stats_file = "proxy_stats.json"
        
        # 加载历史统计数据
        self.load_stats()
        
        # 最小尝试次数，避免新代理因为样本太少而被忽略
        self.min_attempts = 5
        
    def load_stats(self):
        """加载历史统计数据"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for name, stats in data.items():
                        self.stats[name] = stats
                print(f"已加载代理历史统计数据: {len(data)} 个代理")
        except Exception as e:
            print(f"加载代理统计数据失败: {e}")
    
    def save_stats(self):
        """保存统计数据到文件"""
        try:
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(dict(self.stats), f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存代理统计数据失败: {e}")
    
    def get_success_rate(self, proxy_name):
        """计算代理的成功率"""
        stats = self.stats[proxy_name]
        if stats["total"] == 0:
            return 0.5  # 新代理给予中等权重
        return stats["success"] / stats["total"]
    
    def get_proxy_weights(self):
        """计算每个代理的权重"""
        weights = []
        for proxy in self.proxy_servers:
            name = proxy["name"]
            success_rate = self.get_success_rate(name)
            
            # 如果尝试次数太少，给予额外的探索权重
            if self.stats[name]["total"] < self.min_attempts:
                weight = 1.0  # 给新代理更多机会
            else:
                # 基于成功率计算权重，最低权重为0.1，避免完全排除某个代理
                weight = max(0.1, success_rate)
            
            weights.append(weight)
        
        return weights
    
    def select_proxy(self):
        """基于权重随机选择代理"""
        weights = self.get_proxy_weights()
        
        # 加权随机选择
        total_weight = sum(weights)
        if total_weight == 0:
            # 如果所有权重都是0，随机选择
            return random.choice(self.proxy_servers)
        
        # 生成随机数
        rand = random.uniform(0, total_weight)
        
        # 找到对应的代理
        current_weight = 0
        for i, weight in enumerate(weights):
            current_weight += weight
            if rand <= current_weight:
                return self.proxy_servers[i]
        
        # 备用方案
        return self.proxy_servers[-1]
    
    def record_result(self, proxy_name, success, response_time=0):
        """记录代理使用结果"""
        if success:
            self.stats[proxy_name]["success"] += 1
            # 更新平均响应时间
            total = self.stats[proxy_name]["total"]
            current_avg = self.stats[proxy_name]["avg_response_time"]
            self.stats[proxy_name]["avg_response_time"] = (current_avg * total + response_time) / (total + 1)
        else:
            self.stats[proxy_name]["failure"] += 1
        self.stats[proxy_name]["total"] += 1
    
    def print_stats(self):
        """打印统计信息"""
        print("\n=== 代理服务器统计信息 ===")
        print(f"{'代理名称':<10} {'成功次数':<8} {'失败次数':<8} {'总次数':<8} {'成功率':<8} {'平均响应时间':<12} {'权重':<8}")
        print("-" * 80)
        
        weights = self.get_proxy_weights()
        for i, proxy in enumerate(self.proxy_servers):
            name = proxy["name"]
            stats = self.stats[name]
            success_rate = self.get_success_rate(name)
            weight = weights[i]
            avg_time = stats["avg_response_time"]
            
            print(f"{name:<10} {stats['success']:<8} {stats['failure']:<8} {stats['total']:<8} {success_rate:.2%}<8 {avg_time:.2f}s<12 {weight:.2f}<8")
        print("-" * 80)

def test_proxy(proxy_manager, test_count=20):
    """测试智能代理选择系统"""
    print(f"开始测试智能代理选择系统，共进行 {test_count} 次请求...")
    
    for i in range(test_count):
        # 使用智能代理选择
        selected_proxy = proxy_manager.select_proxy()
        proxy_name = selected_proxy["name"]
        
        proxy_settings = {
            "http": selected_proxy["http"],
            "https": selected_proxy["https"]
        }
        
        print(f"\n第 {i+1}/{test_count} 次请求 - 选择代理: {proxy_name}")
        
        try:
            start_time = time.time()
            response = requests.get("https://ifconfig.me", proxies=proxy_settings, timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                ip = response.text.strip()
                print(f"  ✅ 成功 ({response_time:.2f}s) - IP: {ip}")
                proxy_manager.record_result(proxy_name, True, response_time)
            else:
                print(f"  ❌ HTTP错误 {response.status_code}")
                proxy_manager.record_result(proxy_name, False)
                
        except Exception as e:
            print(f"  ❌ 请求失败: {str(e)}")
            proxy_manager.record_result(proxy_name, False)
        
        # 每5次请求显示一次统计
        if (i + 1) % 5 == 0:
            proxy_manager.print_stats()
        
        # 短暂休息
        time.sleep(1)
    
    # 最终统计
    print("\n" + "="*80)
    print("测试完成 - 最终统计结果")
    print("="*80)
    proxy_manager.print_stats()
    
    # 保存统计数据
    proxy_manager.save_stats()
    print("\n统计数据已保存到 proxy_stats.json")

def main():
    print("智能代理选择系统测试")
    print("="*50)
    
    # 创建代理管理器
    proxy_manager = SmartProxyManager()
    
    # 显示初始统计
    proxy_manager.print_stats()
    
    # 询问用户测试次数
    try:
        test_count = input("\n请输入测试次数 (默认20次): ").strip()
        if not test_count:
            test_count = 20
        else:
            test_count = int(test_count)
    except:
        test_count = 20
    
    # 开始测试
    test_proxy(proxy_manager, test_count)

if __name__ == "__main__":
    main()
