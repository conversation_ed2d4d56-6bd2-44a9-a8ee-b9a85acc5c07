#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试品牌.py启动脚本
"""

import sys
import os

def test_import():
    """测试品牌.py的导入"""
    try:
        print("正在测试品牌.py的导入...")
        
        # 测试基本导入
        from collections import defaultdict
        import json
        import random
        import time
        print("✅ 基本模块导入成功")
        
        # 测试SmartProxyManager类的创建
        print("正在测试SmartProxyManager类...")
        
        # 模拟SmartProxyManager类
        class SmartProxyManager:
            def __init__(self):
                self.proxy_servers = [
                    {
                        "name": "服务器1",
                        "http": "http://u1856561711670614:<EMAIL>:36931",
                        "https": "http://u1856561711670614:<EMAIL>:36931"
                    },
                    {
                        "name": "服务器2", 
                        "http": "http://u1856561711670614:<EMAIL>:36930",
                        "https": "http://u1856561711670614:<EMAIL>:36930"
                    }
                ]
                self.stats = defaultdict(lambda: {"success": 0, "failure": 0, "total": 0, "avg_response_time": 0})
                self.stats_file = "proxy_stats.json"
                self.min_attempts = 10
                print("✅ SmartProxyManager 初始化成功")
            
            def select_proxy(self):
                return random.choice(self.proxy_servers)
            
            def record_result(self, proxy_name, success, response_time=0):
                if success:
                    self.stats[proxy_name]["success"] += 1
                else:
                    self.stats[proxy_name]["failure"] += 1
                self.stats[proxy_name]["total"] += 1
            
            def print_stats(self):
                print("=== 代理服务器统计信息 ===")
                for proxy in self.proxy_servers:
                    name = proxy["name"]
                    stats = self.stats[name]
                    success_rate = stats["success"] / max(1, stats["total"])
                    print(f"{name}: 成功率 {success_rate:.1%}, 总次数 {stats['total']}")
        
        # 创建实例测试
        manager = SmartProxyManager()
        print("✅ SmartProxyManager 实例创建成功")
        
        # 测试基本功能
        proxy = manager.select_proxy()
        print(f"✅ 代理选择功能正常: {proxy['name']}")
        
        manager.record_result(proxy['name'], True, 2.5)
        print("✅ 结果记录功能正常")
        
        manager.print_stats()
        print("✅ 统计显示功能正常")
        
        print("\n" + "="*50)
        print("✅ 所有测试通过！智能代理选择系统工作正常")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_asin_loading():
    """测试ASIN加载功能"""
    try:
        print("\n正在测试ASIN加载功能...")
        
        import pandas as pd
        
        # 检查asin.xlsx文件
        if os.path.exists('asin.xlsx'):
            df = pd.read_excel('asin.xlsx')
            print(f"✅ asin.xlsx 文件存在，包含 {len(df)} 行数据")
            print(f"✅ 列名: {list(df.columns)}")
            
            # 查找ASIN列，不区分大小写
            asin_col = None
            for col in df.columns:
                if col.upper() == 'ASIN':
                    asin_col = col
                    break
            
            if asin_col:
                asins = df[asin_col].tolist()
                print(f"✅ 找到ASIN列: {asin_col}")
                print(f"✅ 加载了 {len(asins)} 个ASIN")
                print(f"✅ 前5个ASIN: {asins[:5]}")
                return True
            else:
                print(f"❌ 没有找到ASIN列")
                print(f"可用的列名: {list(df.columns)}")
                return False
        else:
            print("❌ asin.xlsx 文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ ASIN加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("品牌.py 启动测试")
    print("="*50)
    
    # 测试导入和基本功能
    import_success = test_import()
    
    # 测试ASIN加载
    asin_success = test_asin_loading()
    
    print("\n" + "="*50)
    print("测试总结:")
    print(f"导入和基本功能: {'✅ 通过' if import_success else '❌ 失败'}")
    print(f"ASIN加载功能: {'✅ 通过' if asin_success else '❌ 失败'}")
    
    if import_success and asin_success:
        print("\n🎉 所有测试通过！品牌.py 应该可以正常运行")
        print("\n建议:")
        print("1. 运行 python 品牌.py 启动主程序")
        print("2. 运行 python 测试智能代理选择.py 测试代理系统")
    else:
        print("\n⚠️ 部分测试失败，请检查相关问题")
    
    print("="*50)

if __name__ == "__main__":
    main()
