#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理服务器状态检查脚本
"""

import requests
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def test_single_proxy(proxy_info):
    """测试单个代理服务器"""
    name, host, port = proxy_info
    
    proxy_url = f"http://u1856561711690016:F6DYEJanBYcQ@{host}:{port}"
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    test_urls = [
        'https://ifconfig.me',
        'https://httpbin.org/ip',
        'https://www.amazon.com/robots.txt'
    ]
    
    results = {
        'name': name,
        'host': host,
        'port': port,
        'status': 'unknown',
        'response_time': 0,
        'ip': 'unknown',
        'error': None,
        'test_results': []
    }
    
    print(f"正在测试 {name} ({host}:{port})...")
    
    for test_url in test_urls:
        try:
            start_time = time.time()
            response = requests.get(
                test_url, 
                proxies=proxies, 
                timeout=15,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                if 'ifconfig.me' in test_url or 'httpbin.org' in test_url:
                    try:
                        if 'ifconfig.me' in test_url:
                            ip = response.text.strip()
                        else:
                            ip = response.json().get('origin', 'unknown')
                        results['ip'] = ip
                    except:
                        pass
                
                results['test_results'].append({
                    'url': test_url,
                    'status': 'success',
                    'response_time': response_time,
                    'status_code': response.status_code
                })
                
                if results['status'] == 'unknown':
                    results['status'] = 'working'
                    results['response_time'] = response_time
                
            else:
                results['test_results'].append({
                    'url': test_url,
                    'status': 'http_error',
                    'response_time': response_time,
                    'status_code': response.status_code
                })
                
        except requests.exceptions.ProxyError as e:
            results['test_results'].append({
                'url': test_url,
                'status': 'proxy_error',
                'error': str(e)
            })
            if results['status'] == 'unknown':
                results['status'] = 'proxy_error'
                results['error'] = str(e)
                
        except requests.exceptions.Timeout as e:
            results['test_results'].append({
                'url': test_url,
                'status': 'timeout',
                'error': str(e)
            })
            if results['status'] == 'unknown':
                results['status'] = 'timeout'
                results['error'] = str(e)
                
        except requests.exceptions.ConnectionError as e:
            results['test_results'].append({
                'url': test_url,
                'status': 'connection_error',
                'error': str(e)
            })
            if results['status'] == 'unknown':
                results['status'] = 'connection_error'
                results['error'] = str(e)
                
        except Exception as e:
            results['test_results'].append({
                'url': test_url,
                'status': 'unknown_error',
                'error': str(e)
            })
            if results['status'] == 'unknown':
                results['status'] = 'unknown_error'
                results['error'] = str(e)
        
        # 短暂休息避免请求过快
        time.sleep(1)
    
    return results

def main():
    print("123Proxy 代理服务器状态检查")
    print("="*60)
    
    # 定义所有代理服务器
    proxy_servers = [
        ("服务器1", "proxy.123proxy.cn", 36931),
        ("服务器2", "proxy.123proxy.cn", 36930),
        ("服务器3", "proxy.123proxy.cn", 36927),
        ("服务器4", "proxy.123proxy.cn", 36928),
        ("服务器5", "proxy.123proxy.cn", 36929),
    ]
    
    print(f"开始测试 {len(proxy_servers)} 个代理服务器...")
    print("每个代理将测试3个不同的URL")
    print("-" * 60)
    
    # 并发测试所有代理
    results = []
    with ThreadPoolExecutor(max_workers=3) as executor:
        future_to_proxy = {executor.submit(test_single_proxy, proxy): proxy for proxy in proxy_servers}
        
        for future in as_completed(future_to_proxy):
            proxy = future_to_proxy[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f'代理 {proxy[0]} 测试时发生异常: {exc}')
    
    # 按原始顺序排序结果
    results.sort(key=lambda x: [p[0] for p in proxy_servers].index(x['name']))
    
    # 显示测试结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    working_proxies = []
    failed_proxies = []
    
    for result in results:
        status_icon = {
            'working': '✅',
            'proxy_error': '❌',
            'timeout': '⏰',
            'connection_error': '🔌',
            'unknown_error': '❓',
            'unknown': '❓'
        }.get(result['status'], '❓')
        
        print(f"\n{status_icon} {result['name']} ({result['host']}:{result['port']})")
        print(f"   状态: {result['status']}")
        
        if result['status'] == 'working':
            print(f"   响应时间: {result['response_time']:.2f}秒")
            print(f"   代理IP: {result['ip']}")
            working_proxies.append(result)
        else:
            print(f"   错误: {result.get('error', '未知错误')}")
            failed_proxies.append(result)
        
        # 显示详细测试结果
        success_count = sum(1 for test in result['test_results'] if test['status'] == 'success')
        print(f"   测试通过: {success_count}/3")
    
    # 总结
    print("\n" + "="*60)
    print("总结")
    print("="*60)
    print(f"✅ 可用代理: {len(working_proxies)}/{len(proxy_servers)}")
    print(f"❌ 不可用代理: {len(failed_proxies)}/{len(proxy_servers)}")
    
    if working_proxies:
        print(f"\n推荐使用的代理:")
        for proxy in working_proxies:
            print(f"  - {proxy['name']}: {proxy['host']}:{proxy['port']} (响应时间: {proxy['response_time']:.2f}s)")
    
    if failed_proxies:
        print(f"\n需要检查的代理:")
        for proxy in failed_proxies:
            print(f"  - {proxy['name']}: {proxy['status']} - {proxy.get('error', '未知错误')}")
    
    # 建议
    print(f"\n建议:")
    if len(working_proxies) == 0:
        print("❌ 所有代理都不可用，请检查:")
        print("   1. 123Proxy账户是否有剩余流量")
        print("   2. 用户名和密码是否正确")
        print("   3. 网络连接是否正常")
        print("   4. 是否需要联系123Proxy客服")
    elif len(working_proxies) < len(proxy_servers) // 2:
        print("⚠️ 大部分代理不可用，建议:")
        print("   1. 检查123Proxy账户流量")
        print("   2. 暂时只使用可用的代理")
        print("   3. 降低请求频率")
    else:
        print("✅ 大部分代理可用，可以正常使用")
        print("   1. 优先使用响应时间短的代理")
        print("   2. 如果仍有连接错误，可以增加重试次数")

if __name__ == "__main__":
    main()
