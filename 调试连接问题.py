#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试连接问题 - 详细分析代理连接失败的原因
"""

import requests
import time
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback

def get_headers():
    """生成随机请求头"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    ]
    
    return {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0'
    }

def test_proxy_detailed(proxy_info):
    """详细测试单个代理"""
    name, port = proxy_info
    
    proxy_url = f"http://u1856561711690016:<EMAIL>:{port}"
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    print(f"\n{'='*60}")
    print(f"🔍 详细测试 {name} (端口:{port})")
    print(f"{'='*60}")
    
    # 测试不同的URL
    test_urls = [
        ('IP检查', 'https://ifconfig.me'),
        ('Amazon主页', 'https://www.amazon.com'),
        ('Amazon产品页', 'https://www.amazon.com/dp/B08N5WRWNW'),
        ('Amazon搜索', 'https://www.amazon.com/s?k=test'),
        ('HTTP测试', 'http://httpbin.org/ip')
    ]
    
    results = []
    
    for test_name, test_url in test_urls:
        print(f"\n🧪 测试 {test_name}: {test_url}")
        
        try:
            headers = get_headers()
            print(f"   📋 请求头: {headers['User-Agent'][:50]}...")
            print(f"   🌐 代理: {proxy_url}")
            
            start_time = time.time()
            response = requests.get(
                test_url,
                proxies=proxies,
                headers=headers,
                timeout=20,
                allow_redirects=True,
                verify=True
            )
            response_time = time.time() - start_time
            
            print(f"   ✅ 成功: HTTP {response.status_code}")
            print(f"   ⏱️ 响应时间: {response_time:.2f}秒")
            print(f"   📏 响应长度: {len(response.text)} 字符")
            
            # 检查响应内容
            if 'amazon' in test_url.lower():
                if 'captcha' in response.text.lower():
                    print(f"   ⚠️ 检测到验证码页面")
                elif 'robot' in response.text.lower():
                    print(f"   ⚠️ 检测到机器人检测")
                elif 'blocked' in response.text.lower():
                    print(f"   ⚠️ 检测到访问被阻止")
                else:
                    print(f"   ✅ 页面内容正常")
            
            results.append({
                'test': test_name,
                'url': test_url,
                'status': 'success',
                'status_code': response.status_code,
                'response_time': response_time,
                'content_length': len(response.text)
            })
            
        except requests.exceptions.ProxyError as e:
            print(f"   ❌ 代理错误: {str(e)}")
            print(f"   🔍 详细信息: {type(e).__name__}")
            results.append({
                'test': test_name,
                'url': test_url,
                'status': 'proxy_error',
                'error': str(e),
                'error_type': type(e).__name__
            })
            
        except requests.exceptions.Timeout as e:
            print(f"   ⏰ 超时错误: {str(e)}")
            results.append({
                'test': test_name,
                'url': test_url,
                'status': 'timeout',
                'error': str(e)
            })
            
        except requests.exceptions.ConnectionError as e:
            print(f"   🔌 连接错误: {str(e)}")
            print(f"   🔍 错误类型: {type(e).__name__}")
            # 尝试解析更详细的错误信息
            error_str = str(e)
            if 'Connection refused' in error_str:
                print(f"   📝 分析: 代理服务器拒绝连接")
            elif 'timeout' in error_str.lower():
                print(f"   📝 分析: 连接超时")
            elif 'name resolution' in error_str.lower():
                print(f"   📝 分析: DNS解析失败")
            elif 'authentication' in error_str.lower():
                print(f"   📝 分析: 代理认证失败")
            else:
                print(f"   📝 分析: 未知连接问题")
                
            results.append({
                'test': test_name,
                'url': test_url,
                'status': 'connection_error',
                'error': str(e),
                'error_type': type(e).__name__
            })
            
        except requests.exceptions.SSLError as e:
            print(f"   🔒 SSL错误: {str(e)}")
            results.append({
                'test': test_name,
                'url': test_url,
                'status': 'ssl_error',
                'error': str(e)
            })
            
        except Exception as e:
            print(f"   ❓ 未知错误: {str(e)}")
            print(f"   🔍 错误类型: {type(e).__name__}")
            print(f"   📋 完整错误信息:")
            traceback.print_exc()
            results.append({
                'test': test_name,
                'url': test_url,
                'status': 'unknown_error',
                'error': str(e),
                'error_type': type(e).__name__
            })
        
        # 短暂休息
        time.sleep(2)
    
    return name, port, results

def analyze_results(all_results):
    """分析所有测试结果"""
    print(f"\n{'='*80}")
    print(f"📊 测试结果分析")
    print(f"{'='*80}")
    
    total_tests = 0
    success_tests = 0
    error_types = {}
    
    for proxy_name, port, results in all_results:
        print(f"\n🔧 {proxy_name} (端口:{port}) 总结:")
        proxy_success = 0
        proxy_total = len(results)
        
        for result in results:
            total_tests += 1
            if result['status'] == 'success':
                success_tests += 1
                proxy_success += 1
                print(f"   ✅ {result['test']}: 成功 ({result['response_time']:.2f}s)")
            else:
                error_type = result['status']
                error_types[error_type] = error_types.get(error_type, 0) + 1
                print(f"   ❌ {result['test']}: {error_type}")
                if 'error' in result:
                    print(f"      💬 {result['error'][:100]}...")
        
        success_rate = (proxy_success / proxy_total) * 100
        print(f"   📈 成功率: {success_rate:.1f}% ({proxy_success}/{proxy_total})")
    
    print(f"\n{'='*80}")
    print(f"🎯 总体统计")
    print(f"{'='*80}")
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {success_tests}")
    print(f"总成功率: {(success_tests/total_tests)*100:.1f}%")
    
    print(f"\n🔍 错误类型分布:")
    for error_type, count in error_types.items():
        percentage = (count / total_tests) * 100
        print(f"   {error_type}: {count} 次 ({percentage:.1f}%)")
    
    # 给出建议
    print(f"\n💡 建议:")
    if success_tests / total_tests > 0.8:
        print("✅ 代理整体工作正常，偶发错误属于正常现象")
    elif success_tests / total_tests > 0.5:
        print("⚠️ 代理部分可用，建议检查网络环境或代理配置")
    else:
        print("❌ 代理大量失败，建议检查:")
        print("   1. 代理账户是否有效")
        print("   2. 网络连接是否稳定")
        print("   3. 是否被目标网站限制")

def main():
    print("🔍 代理连接问题详细调试")
    print("="*60)
    
    # 定义所有代理服务器
    proxy_servers = [
        ("服务器1", 36931),
        ("服务器2", 36930),
        ("服务器3", 36927),
        ("服务器4", 36928),
        ("服务器5", 36929),
    ]
    
    print(f"将对 {len(proxy_servers)} 个代理服务器进行详细测试...")
    print("每个代理将测试5个不同的URL")
    
    all_results = []
    
    # 串行测试，避免并发导致的问题
    for proxy_info in proxy_servers:
        try:
            name, port, results = test_proxy_detailed(proxy_info)
            all_results.append((name, port, results))
        except Exception as e:
            print(f"❌ 测试 {proxy_info[0]} 时发生异常: {str(e)}")
            traceback.print_exc()
    
    # 分析结果
    if all_results:
        analyze_results(all_results)
    else:
        print("❌ 没有获得任何测试结果")

if __name__ == "__main__":
    main()
