#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
123Proxy配置助手

此脚本帮助您配置123Proxy隧道代理。
请按照以下步骤操作：

1. 登录123Proxy控制台: https://console.123proxy.cn/
2. 进入"我的套餐"或"产品中心"
3. 点击"立即生成"获取代理信息
4. 运行此脚本，输入获取到的代理信息
"""

import requests
import json
import os

def get_proxy_config():
    """获取用户输入的代理配置"""
    print("123Proxy配置助手")
    print("=" * 50)
    print("请从123Proxy控制台获取以下信息：")
    print()
    
    # 获取用户输入
    username = input("请输入用户名 (当前: u1856561711670614): ").strip()
    if not username:
        username = "u1856561711670614"
    
    password = input("请输入密码 (当前: P0BI4UunKepU): ").strip()
    if not password:
        password = "P0BI4UunKepU"
    
    print("\n请输入代理服务器信息：")
    print("(从123Proxy控制台的'代理主机'和'代理端口'获取)")
    
    servers = []
    while True:
        print(f"\n--- 服务器 {len(servers) + 1} ---")
        host = input("代理主机 (例如: us.gateway.123proxy.cn): ").strip()
        if not host:
            if len(servers) == 0:
                print("至少需要输入一个服务器地址!")
                continue
            else:
                break
        
        port_str = input("代理端口 (按流量套餐默认: 31000): ").strip()
        if not port_str:
            port = 31000  # 按流量套餐的标准端口
        else:
            try:
                port = int(port_str)
            except ValueError:
                print("端口必须是数字，使用默认端口31000")
                port = 31000
        
        name = input(f"服务器名称 (可选): ").strip()
        if not name:
            name = f"服务器{len(servers) + 1}"
        
        servers.append({
            "host": host,
            "port": port,
            "name": name
        })
        
        more = input("是否添加更多服务器? (y/N): ").strip().lower()
        if more not in ['y', 'yes']:
            break
    
    return {
        "username": username,
        "password": password,
        "servers": servers
    }

def test_proxy_server(config, server):
    """测试单个代理服务器"""
    try:
        proxy_url = f"http://{config['username']}:{config['password']}@{server['host']}:{server['port']}"
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        
        response = requests.get(
            'http://httpbin.org/ip',
            proxies=proxies,
            timeout=10,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        
        if response.status_code == 200:
            ip_info = response.json()
            proxy_ip = ip_info.get('origin', 'Unknown')
            return True, proxy_ip
        else:
            return False, f"HTTP {response.status_code}"
            
    except requests.exceptions.ProxyError as e:
        return False, f"代理错误: {str(e)}"
    except requests.exceptions.Timeout:
        return False, "连接超时"
    except requests.exceptions.ConnectionError as e:
        return False, f"连接错误: {str(e)}"
    except Exception as e:
        return False, f"未知错误: {str(e)}"

def test_all_servers(config):
    """测试所有服务器"""
    print("\n开始测试代理服务器...")
    print("-" * 50)
    
    working_servers = []
    
    for server in config['servers']:
        print(f"测试 {server['name']} ({server['host']}:{server['port']})...")
        
        success, result = test_proxy_server(config, server)
        
        if success:
            print(f"✅ {server['name']} 连接成功! 代理IP: {result}")
            working_servers.append(server)
        else:
            print(f"❌ {server['name']} 连接失败: {result}")
    
    return working_servers

def generate_config_code(config, working_servers):
    """生成配置代码"""
    print("\n" + "=" * 50)
    print("配置代码生成")
    print("=" * 50)
    
    if working_servers:
        print(f"找到 {len(working_servers)} 个可用的代理服务器")
        print("\n请将以下代码复制到主程序中：")
        print("-" * 30)
        
        print("# 123Proxy配置")
        print(f'self.proxy_123_username = "{config["username"]}"')
        print(f'self.proxy_123_password = "{config["password"]}"')
        print("self.proxy_123_servers = [")
        
        for server in working_servers:
            print(f'    {{"host": "{server["host"]}", "port": {server["port"]}}},  # {server["name"]}')
        
        print("]")
        print("-" * 30)
        
        # 保存配置到文件
        config_data = {
            "username": config["username"],
            "password": config["password"],
            "servers": working_servers
        }
        
        with open("123proxy_config.json", "w", encoding="utf-8") as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n配置已保存到: 123proxy_config.json")
        
    else:
        print("❌ 没有找到可用的代理服务器!")
        print("\n请检查:")
        print("1. 代理服务器地址和端口是否正确")
        print("2. 用户名和密码是否正确")
        print("3. 123Proxy套餐是否还有剩余流量")
        print("4. 网络连接是否正常")

def main():
    """主函数"""
    try:
        # 获取配置
        config = get_proxy_config()
        
        # 测试服务器
        working_servers = test_all_servers(config)
        
        # 生成配置代码
        generate_config_code(config, working_servers)
        
        print("\n配置完成!")
        
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")

if __name__ == "__main__":
    main()
