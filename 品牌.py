"""
Amazon产品信息爬虫 - 已集成123Proxy隧道代理支持

123Proxy配置说明：
1. 登录123Proxy控制台: https://console.123proxy.cn/
2. 进入"我的套餐"或"产品中心"
3. 点击"立即生成"获取代理信息
4. 复制"代理主机"和"代理端口"信息
5. 在代码中的proxy_123_servers列表中更新正确的服务器地址和端口

当前配置的用户名: u1856561711670614
当前配置的密码: P0BI4UunKepU
代理服务器: proxy.123proxy.cn:36931
流量套餐: 0.8G

所有4个代码文件已统一使用相同的123Proxy配置：
- 专利.py: 已配置123Proxy代理 ✅
- 价格.py: 已配置123Proxy代理 ✅
- 采集.py: 已配置123Proxy代理 ✅
- 品牌.py: 已配置123Proxy代理 ✅

如果代理连接失败，请检查：
- 代理服务器地址是否正确
- 端口号是否正确
- 用户名密码是否正确
- 流量是否已用完
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import random
import hashlib
import os
import datetime
import pandas as pd
import threading
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor  # 明确导入ThreadPoolExecutor
from pathlib import Path
from queue import Queue, Empty
from collections import defaultdict
from tqdm import tqdm
import base64
import io
import pickle
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from urllib.parse import urljoin

# 智能代理选择系统
class SmartProxyManager:
    def __init__(self):
        # 定义所有代理服务器
        self.proxy_servers = [
            {
                "name": "服务器1",
                "http": "http://u1856561711690016:<EMAIL>:36931",
                "https": "http://u1856561711690016:<EMAIL>:36931"
            },
            {
                "name": "服务器2",
                "http": "http://u1856561711690016:<EMAIL>:36930",
                "https": "http://u1856561711690016:<EMAIL>:36930"
            },
            {
                "name": "服务器3",
                "http": "http://u1856561711690016:<EMAIL>:36927",
                "https": "http://u1856561711690016:<EMAIL>:36927"
            },
            {
                "name": "服务器4",
                "http": "http://u1856561711690016:<EMAIL>:36928",
                "https": "http://u1856561711690016:<EMAIL>:36928"
            },
            {
                "name": "服务器5",
                "http": "http://u1856561711690016:<EMAIL>:36929",
                "https": "http://u1856561711690016:<EMAIL>:36929"
            }
        ]

        # 统计数据
        self.stats = defaultdict(lambda: {"success": 0, "failure": 0, "total": 0, "avg_response_time": 0})
        self.stats_file = "proxy_stats.json"

        # 加载历史统计数据
        self.load_stats()

        # 线程锁，保护统计数据
        self.stats_lock = threading.Lock()

        # 最小尝试次数，避免新代理因为样本太少而被忽略
        self.min_attempts = 10

    def load_stats(self):
        """加载历史统计数据"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for name, stats in data.items():
                        self.stats[name] = stats
                print(f"已加载代理历史统计数据: {len(data)} 个代理")
        except Exception as e:
            print(f"加载代理统计数据失败: {e}")

    def save_stats(self):
        """保存统计数据到文件"""
        try:
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(dict(self.stats), f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存代理统计数据失败: {e}")

    def get_success_rate(self, proxy_name):
        """计算代理的成功率"""
        stats = self.stats[proxy_name]
        if stats["total"] == 0:
            return 0.5  # 新代理给予中等权重
        return stats["success"] / stats["total"]

    def get_proxy_weights(self):
        """计算每个代理的权重 - 综合考虑成功率和响应时间"""
        weights = []
        for proxy in self.proxy_servers:
            name = proxy["name"]
            success_rate = self.get_success_rate(name)
            avg_response_time = self.stats[name]["avg_response_time"]

            # 如果尝试次数太少，给予额外的探索权重
            if self.stats[name]["total"] < self.min_attempts:
                weight = 1.0  # 给新代理更多机会
            else:
                # 综合成功率和响应时间计算权重
                # 成功率权重：70%，响应时间权重：30%
                success_weight = success_rate * 0.7

                # 响应时间权重：响应时间越短权重越高
                # 假设理想响应时间为2秒，超过10秒权重为0
                if avg_response_time > 0:
                    time_weight = max(0, (10 - avg_response_time) / 10) * 0.3
                else:
                    time_weight = 0.15  # 没有响应时间数据时给予中等权重

                weight = max(0.1, success_weight + time_weight)

            weights.append(weight)

        return weights

    def select_proxy(self):
        """基于权重随机选择代理"""
        weights = self.get_proxy_weights()

        # 加权随机选择
        total_weight = sum(weights)
        if total_weight == 0:
            # 如果所有权重都是0，随机选择
            return random.choice(self.proxy_servers)

        # 生成随机数
        rand = random.uniform(0, total_weight)

        # 找到对应的代理
        current_weight = 0
        for i, weight in enumerate(weights):
            current_weight += weight
            if rand <= current_weight:
                return self.proxy_servers[i]

        # 备用方案
        return self.proxy_servers[-1]

    def record_result(self, proxy_name, success, response_time=0):
        """记录代理使用结果"""
        with self.stats_lock:
            if success:
                self.stats[proxy_name]["success"] += 1
                # 更新平均响应时间
                total = self.stats[proxy_name]["total"]
                current_avg = self.stats[proxy_name]["avg_response_time"]
                self.stats[proxy_name]["avg_response_time"] = (current_avg * total + response_time) / (total + 1)
            else:
                self.stats[proxy_name]["failure"] += 1
            self.stats[proxy_name]["total"] += 1

    def print_stats(self):
        """打印统计信息"""
        print("\n=== 代理服务器统计信息 ===")
        print(f"{'代理名称':<10} {'成功次数':<8} {'失败次数':<8} {'总次数':<8} {'成功率':<8} {'平均响应时间':<12} {'权重':<8}")
        print("-" * 80)

        weights = self.get_proxy_weights()
        for i, proxy in enumerate(self.proxy_servers):
            name = proxy["name"]
            stats = self.stats[name]
            success_rate = self.get_success_rate(name)
            weight = weights[i]
            avg_time = stats["avg_response_time"]

            print(f"{name:<10} {stats['success']:<8} {stats['failure']:<8} {stats['total']:<8} {success_rate:.2%}<8 {avg_time:.2f}s<12 {weight:.2f}<8")
        print("-" * 80)

# 创建全局代理管理器实例
smart_proxy_manager = SmartProxyManager()
import urllib.parse

# 导入代理认证插件模块
try:
    from proxy_auth_plugin import create_proxy_auth_extension
except ImportError:
    try:
        from proxy_auth_plugin import create_proxy_auth_extension
    except ImportError:
        print("警告: 无法导入代理认证插件模块，部分功能可能受限")

# 尝试导入必要的库，失败时在main中处理
try:
    from amazoncaptcha import AmazonCaptcha
except ImportError:
    pass

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
except ImportError:
    pass

class AmazonProductScraper:
    def __init__(self):
        # 初始化会话
        self.session = requests.Session()
        self.session.headers = self.Headers()

        # 创建锁，用于线程安全
        self.lock = threading.Lock()

        # 智能代理选择系统配置
        self.use_smart_proxy = True  # 设置为True使用智能代理选择
        self.smart_proxy_manager = smart_proxy_manager  # 使用全局代理管理器

        # 123Proxy隧道代理配置（保持兼容性）
        self.use_123proxy = True  # 设置为True使用123Proxy隧道代理

        # 尝试从配置文件加载123Proxy设置
        self.load_123proxy_config()

        # 如果没有配置文件，使用默认配置
        if not hasattr(self, 'proxy_123_username'):
            self.proxy_123_username = "u1856561711690016"  # 您的123Proxy用户名
            self.proxy_123_password = "F6DYEJanBYcQ"       # 您的123Proxy密码

            # 123Proxy按流量套餐服务器配置 - 使用智能代理管理器的服务器
            self.proxy_123_servers = [
                {"host": "proxy.123proxy.cn", "port": 36931},
                {"host": "proxy.123proxy.cn", "port": 36930},
                {"host": "proxy.123proxy.cn", "port": 36927},
                {"host": "proxy.123proxy.cn", "port": 36928},
                {"host": "proxy.123proxy.cn", "port": 36929},
            ]

            print("使用智能代理选择系统配置")

        # 加载代理（优先使用智能代理选择）
        if self.use_smart_proxy:
            self.proxies = self.setup_smart_proxy_system()
        elif self.use_123proxy:
            self.proxies = self.setup_123proxy_tunnel()
            # 初始化123Proxy的IP池和队列系统
            self.init_123proxy_pool()
        else:
            self.proxies = self.load_proxies()
        self.invalid_proxies = []
        
        # 用于跟踪代理失败信息
        self.proxy_failures = {}  # 存储代理失败信息：{proxy_str: {'failures': 次数, 'last_failure': 时间}}
        
        print(f"已加载 {len(self.proxies)} 个代理")
        
        # 禁用cookies，减少被跟踪风险
        self.session.cookies.clear()
        
        # 设置请求延迟和重试参数
        self.download_delay = 2  # 基础请求延迟(秒)
        self.download_delay_randomize = 0.5  # 随机化因子(0.5表示±50%)
        self.max_retries = 5  # 最大重试次数（可通过界面配置）
        self.retry_delay = 5  # 重试基础延迟(秒)
        self.retry_codes = [500, 502, 503, 504, 522, 524, 408, 429]  # 触发重试的HTTP状态码
        
        # 定义存储各类ASIN的列表
        self.timeout_asins = []
        self.non_unique_brand_asins = []
        self.non_compliant_asins = []
        self.failed_to_get_info_asins = []  # 新增：无法获取产品信息的ASIN
        self.visit_the_brand_asins = []
        self.other_brand_asins = []
        self.processed_asins = set()

        # 性能优化：缓存已读取的表格数据
        self._brand_cache = {}
        self._cache_loaded = False
        
        # 记录处理进度
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        
        # 停止事件，用于中断线程
        self.stop_event = threading.Event()
        
        # 进度条
        self.progress_bar = None
        
        # 用于保存cookies的文件
        self.cookie_file = 'amazon_cookies.pkl'
        
        # 加载之前保存的cookies
        self.load_cookies_from_file()
        
        # 存储最终有效结果
        self.valid_results = []
        
        # 批量保存计数器
        self.batch_size = 20
        
        self.checkpoint_file = "scraper_checkpoint.txt"
        self.current_position = self.load_checkpoint()
        
        # 初始化时尝试加载保存的cookies，如果没有则更新美国区域cookies
        if not self.load_cookies_from_file():
            print("未找到保存的cookies或已过期，更新美国区域cookies")
            self.update_us_cookies()
            
        self.driver = None
        self.current_main_asin = None  # 当前正在处理的主ASIN
        self.asins = []  # 存储从asin.xlsx加载的所有主ASIN
        
        # 创建日志文件
        self.log_file = "scraper_log.txt"
        
        # 添加代理黑名单字典，保存IP和暂停时间
        self.suspended_proxies = {}

    def load_123proxy_config(self):
        """从配置文件加载123Proxy设置"""
        try:
            config_file = "123proxy_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                self.proxy_123_username = config.get('username', '')
                self.proxy_123_password = config.get('password', '')
                self.proxy_123_servers = config.get('servers', [])

                print(f"已从 {config_file} 加载123Proxy配置")
                print(f"用户名: {self.proxy_123_username}")
                print(f"服务器数量: {len(self.proxy_123_servers)}")
                return True
            else:
                print(f"未找到配置文件: {config_file}")
                return False

        except Exception as e:
            print(f"加载123Proxy配置时出错: {str(e)}")
            return False

    def test_123proxy_server(self, host, port, timeout=10):
        """测试单个123Proxy服务器是否可用"""
        try:
            proxy_url = f"http://{self.proxy_123_username}:{self.proxy_123_password}@{host}:{port}"
            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }

            # 使用简单的HTTP请求测试代理
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxies,
                timeout=timeout,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )

            if response.status_code == 200:
                ip_info = response.json()
                proxy_ip = ip_info.get('origin', 'Unknown')
                print(f"✅ 123Proxy服务器 {host}:{port} 可用 (代理IP: {proxy_ip})")
                return True
            elif response.status_code == 503:
                # 503可能是临时过载，但代理本身可能是可用的
                print(f"⚠️  123Proxy服务器 {host}:{port} 临时过载 (503)，但将其标记为可用")
                return True
            else:
                print(f"❌ 123Proxy服务器 {host}:{port} 响应异常 (状态码: {response.status_code})")
                return False

        except requests.exceptions.ProxyError:
            print(f"❌ 123Proxy服务器 {host}:{port} 代理认证失败")
            return False
        except requests.exceptions.Timeout:
            print(f"❌ 123Proxy服务器 {host}:{port} 连接超时")
            return False
        except requests.exceptions.ConnectionError:
            print(f"❌ 123Proxy服务器 {host}:{port} 连接失败")
            return False
        except Exception as e:
            print(f"❌ 123Proxy服务器 {host}:{port} 测试失败: {str(e)}")
            return False

    def setup_123proxy_tunnel(self):
        """设置123Proxy隧道代理"""
        proxies = []

        print("正在配置123Proxy隧道代理...")
        print(f"用户名: {self.proxy_123_username}")
        print(f"测试服务器数量: {len(self.proxy_123_servers)}")
        print("-" * 50)

        working_servers = []

        # 对于123Proxy，我们直接添加代理而不进行严格测试
        # 因为测试可能因为临时过载而失败，但代理本身是可用的
        print("跳过严格的代理测试，直接使用配置的123Proxy服务器")

        for server in self.proxy_123_servers:
            host = server["host"]
            port = server["port"]

            print(f"添加123Proxy服务器: {host}:{port}")
            working_servers.append(server)

            # 构建代理URL - 123Proxy通常使用HTTP代理协议
            proxy_url = f"http://{self.proxy_123_username}:{self.proxy_123_password}@{host}:{port}"

            # 创建代理字典
            proxy = {
                'http': proxy_url,
                'https': proxy_url  # 同时设置https代理
            }

            proxies.append(proxy)

        print("-" * 50)
        if proxies:
            print(f"✅ 成功配置 {len(proxies)} 个可用的123Proxy隧道代理服务器")
            print("可用服务器列表:")
            for server in working_servers:
                print(f"  - {server['host']}:{server['port']}")
        else:
            print("❌ 没有找到可用的123Proxy代理服务器!")
            print("请检查:")
            print("1. 用户名和密码是否正确")
            print("2. 123Proxy套餐是否还有剩余流量")
            print("3. 网络连接是否正常")
            print("4. 服务器地址是否正确")
            print("建议运行 test_123proxy.py 脚本进行详细测试")

        return proxies

    def setup_smart_proxy_system(self):
        """设置智能代理选择系统"""
        proxies = []

        print("正在配置智能代理选择系统...")
        print(f"可用代理服务器数量: {len(self.smart_proxy_manager.proxy_servers)}")
        print("-" * 50)

        # 将智能代理管理器的代理服务器转换为标准格式
        for proxy_server in self.smart_proxy_manager.proxy_servers:
            proxy = {
                'http': proxy_server['http'],
                'https': proxy_server['https'],
                'name': proxy_server['name']  # 添加名称用于统计
            }
            proxies.append(proxy)
            print(f"添加代理服务器: {proxy_server['name']}")

        print("-" * 50)
        print(f"✅ 智能代理选择系统配置完成，共 {len(proxies)} 个代理服务器")

        # 打印当前统计信息
        self.smart_proxy_manager.print_stats()

        return proxies

    def get_smart_proxy(self):
        """使用智能代理选择系统获取代理"""
        if not self.use_smart_proxy:
            return random.choice(self.proxies) if self.proxies else None

        # 使用智能代理管理器选择代理
        selected_proxy_server = self.smart_proxy_manager.select_proxy()

        # 转换为标准代理格式
        proxy = {
            'http': selected_proxy_server['http'],
            'https': selected_proxy_server['https'],
            'name': selected_proxy_server['name']
        }

        return proxy

    def record_proxy_result(self, proxy, success, response_time=0):
        """记录代理使用结果到智能代理管理器"""
        if not self.use_smart_proxy or not proxy:
            return

        # 获取代理名称
        proxy_name = proxy.get('name')
        if not proxy_name:
            # 如果没有名称，尝试从URL中推断
            proxy_url = proxy.get('http', '')
            for server in self.smart_proxy_manager.proxy_servers:
                if server['http'] == proxy_url:
                    proxy_name = server['name']
                    break

        if proxy_name:
            self.smart_proxy_manager.record_result(proxy_name, success, response_time)

    def save_proxy_stats(self):
        """保存代理统计数据"""
        if self.use_smart_proxy:
            self.smart_proxy_manager.save_stats()

    def print_proxy_stats(self):
        """打印代理统计信息"""
        if self.use_smart_proxy:
            self.smart_proxy_manager.print_stats()

    def set_max_retries(self, retries):
        """设置最大重试次数"""
        if isinstance(retries, int) and retries > 0:
            self.max_retries = retries
            print(f"已设置最大重试次数为: {retries}")
        else:
            print("重试次数必须是大于0的整数")

    def get_max_retries(self):
        """获取当前最大重试次数"""
        return self.max_retries

    def add_non_compliant_asin(self, asin, reason, main_asin=None):
        """安全地添加不合规ASIN，确保格式正确"""
        if not main_asin:
            main_asin = asin

        non_compliant_item = {
            'asin': str(asin),
            'reason': str(reason),
            'main_asin': str(main_asin)
        }

        with self.lock:
            self.non_compliant_asins.append(non_compliant_item)
            self.check_batch_save_non_compliant()

        # 简化日志：只在调试模式下输出详细信息
        pass

    def add_failed_to_get_info_asin(self, asin, brand=None, error_reason=None):
        """安全地添加无法获取信息的ASIN"""
        # 如果没有提供品牌信息，尝试从主表格中查找
        if not brand or str(brand).strip() == '' or str(brand) == 'Unknown':
            brand_from_table = self.get_brand_from_main_table(asin)
            if brand_from_table:
                brand = brand_from_table
            else:
                brand = 'Unknown'

        failed_item = {
            'asin': str(asin),
            'brand': str(brand),
            'error_reason': str(error_reason) if error_reason else '无法获取产品信息，已达到重试次数上限',
            'main_asin': str(asin)
        }

        with self.lock:
            self.failed_to_get_info_asins.append(failed_item)
            self.check_batch_save_failed_to_get_info()

        # 简化日志：只显示关键信息
        if brand != 'Unknown':
            print(f"ASIN {asin}: 已添加到重试列表 (品牌: {brand})")
        else:
            print(f"ASIN {asin}: 已添加到重试列表")

    def _load_brand_cache(self):
        """加载品牌缓存，只在第一次调用时执行"""
        if self._cache_loaded:
            return

        try:
            possible_files = [
                "Amazon产品信息.xlsx",
                "Visit_the_Brand_ASINs.xlsx",
                "Other_Brand_ASINs.xlsx",
                "asin.xlsx"
            ]

            for filename in possible_files:
                if os.path.exists(filename):
                    try:
                        df = pd.read_excel(filename)

                        # 查找ASIN列，不区分大小写
                        asin_col = None
                        for col in df.columns:
                            if col.upper() == 'ASIN':
                                asin_col = col
                                break

                        if asin_col and '品牌' in df.columns:
                            # 将ASIN和品牌信息加入缓存
                            for _, row in df.iterrows():
                                asin = row[asin_col]
                                brand = row['品牌']
                                if brand and str(brand).strip() and str(brand) != 'nan':
                                    if asin not in self._brand_cache:  # 优先级：先加载的文件优先
                                        self._brand_cache[asin] = str(brand)
                    except Exception:
                        continue

            self._cache_loaded = True
            # 简化日志：只在有大量数据时显示
            if len(self._brand_cache) > 100:
                print(f"已缓存 {len(self._brand_cache)} 个ASIN的品牌信息")

        except Exception:
            pass

    def get_brand_from_main_table(self, asin):
        """从主表格中获取ASIN对应的品牌信息（使用缓存）"""
        self._load_brand_cache()
        return self._brand_cache.get(asin)

    def init_123proxy_pool(self):
        """初始化123Proxy的IP池和队列系统"""
        from queue import Queue

        # 初始化IP池
        self.ip_pool_123 = []
        self.ip_pool_lock_123 = threading.Lock()

        # 预加载5个123Proxy代理配置
        for _ in range(5):
            proxy = self.get_123proxy_address()
            self.ip_pool_123.append(proxy)

        # 用于队列操作
        self.proxy_queue_123 = Queue()
        for proxy in self.ip_pool_123:
            self.proxy_queue_123.put(proxy)

        print(f"预加载了 {len(self.ip_pool_123)} 个123Proxy代理配置")

    def get_123proxy_address(self, force_refresh=False):
        """获取123Proxy代理配置，每次调用都会获得不同的IP"""
        # 123Proxy配置
        username = self.proxy_123_username
        password = self.proxy_123_password
        host = self.proxy_123_servers[0]["host"]  # 使用第一个服务器
        port = self.proxy_123_servers[0]["port"]

        # 添加随机参数来确保每次获得不同的IP
        if force_refresh or random.random() > 0.5:
            timestamp = str(int(time.time() * 1000))
            random_str = hashlib.md5((timestamp + str(random.randint(1, 10000))).encode()).hexdigest()[:8]
            # 123Proxy隧道代理通过每次新的连接自动分配不同IP
            # 我们通过添加随机session参数来确保每次都是新连接
            proxy_url = f"http://{username}:{password}@{host}:{port}?session={random_str}"
        else:
            proxy_url = f"http://{username}:{password}@{host}:{port}"

        proxies = {
            "http": proxy_url,
            "https": proxy_url,
        }
        return proxies

    def get_new_123proxy(self):
        """获取新的123Proxy代理，优先从池中获取，池为空时生成新代理"""
        with self.ip_pool_lock_123:
            if self.ip_pool_123 and len(self.ip_pool_123) > 0:
                # 从池中取一个代理配置
                proxy = self.ip_pool_123.pop(0)
                # 异步补充IP池
                threading.Thread(target=self._refill_123proxy_pool).start()
                return proxy
            else:
                # 池为空，直接生成新代理配置
                return self.get_123proxy_address(force_refresh=True)

    def _refill_123proxy_pool(self):
        """异步补充123Proxy IP池"""
        try:
            # 生成新的代理配置
            new_proxy = self.get_123proxy_address(force_refresh=True)
            # 安全地添加到池中
            with self.ip_pool_lock_123:
                if len(self.ip_pool_123) < 10:  # 限制池大小
                    self.ip_pool_123.append(new_proxy)
        except Exception as e:
            print(f"补充123Proxy IP池时出错: {str(e)}")

    def load_proxies(self):
        """从文件中加载代理信息，使用改进的格式处理"""
        proxies = []
        try:
            if not os.path.exists('proxies.txt'):
                print(f"代理文件不存在: proxies.txt")
                return proxies

            with open('proxies.txt', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用更健壮的方式分割代理块
            proxy_blocks = content.strip().split('\n\n')
            print(f"找到 {len(proxy_blocks)} 个代理块")
            
            for block in proxy_blocks:
                if not block.strip():
                    continue
                
                # 使用正则表达式提取信息，更加健壮
                protocol = re.search(r'协议:\s*(\w+)', block)
                host = re.search(r'地址:\s*([0-9.]+)', block)
                port = re.search(r'端口:\s*(\d+)', block)
                username = re.search(r'用户名:\s*(\S+)', block)
                password = re.search(r'密码:\s*(\S+)', block)
                
                if protocol and host and port and username and password:
                    protocol_value = protocol.group(1).lower()
                    host_value = host.group(1)
                    port_value = port.group(1)
                    username_value = username.group(1)
                    password_value = password.group(1)
                    
                    # 构建代理URL
                    proxy_url = f"{protocol_value}://{username_value}:{password_value}@{host_value}:{port_value}"
                    
                    # 创建代理字典
                    proxy = {
                        'http': proxy_url,
                        'https': proxy_url  # 同时设置https代理
                    }
                    
                    proxies.append(proxy)
            
            print(f"成功加载 {len(proxies)} 个代理")
            
            # 如果没有找到任何代理，打印警告
            if not proxies:
                print("警告: 没有从文件中找到有效的代理")
                
            return proxies
        except Exception as e:
            print(f"加载代理时出错: {str(e)}")
            return []
    
    def md5_encrypt(self, string):
        md5 = hashlib.md5()
        md5.update(string.encode('utf-8'))
        return md5.hexdigest()

    def Headers(self):
        """生成随机的请求头以减少被检测的可能性并确保获取英文页面"""
        # 创建更多User-Agent变体 - 使用更现代的浏览器版本和更多平台
        user_agents = [
            f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{random.randint(535, 537)}.{random.randint(34, 36)} (KHTML, like Gecko) Chrome/{random.randint(109, 130)}.0.0.0 Safari/{random.randint(535, 537)}.{random.randint(34, 36)}",
            f"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:{random.randint(100, 120)}.0) Gecko/20100101 Firefox/{random.randint(100, 120)}.0",
            f"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_{random.randint(14, 15)}_{random.randint(1, 7)}) AppleWebKit/{random.randint(600, 605)}.{random.randint(1, 7)}.{random.randint(1, 10)} (KHTML, like Gecko) Version/{random.randint(14, 16)}.{random.randint(0, 7)}.{random.randint(1, 10)} Safari/{random.randint(600, 605)}.{random.randint(1, 7)}.{random.randint(1, 10)}",
            f"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/{random.randint(535, 537)}.{random.randint(34, 36)} (KHTML, like Gecko) Chrome/{random.randint(109, 130)}.0.0.0 Safari/{random.randint(535, 537)}.{random.randint(34, 36)}",
            f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{random.randint(535, 537)}.{random.randint(34, 36)} (KHTML, like Gecko) Chrome/{random.randint(109, 130)}.0.0.0 Edge/{random.randint(100, 120)}.0.{random.randint(1000, 2000)}.{random.randint(10, 99)}",
            f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{random.randint(535, 537)}.{random.randint(34, 36)} (KHTML, like Gecko) Chrome/{random.randint(109, 130)}.0.0.0 Edg/{random.randint(100, 120)}.0.{random.randint(1000, 2000)}.{random.randint(10, 99)}"
        ]
        
        # 随机选择一个User-Agent
        user_agent = random.choice(user_agents)
        
        # 只使用英语作为语言，优化语言参数强制使用美国英语
        accept_language = "en-US,en;q=0.9"
        
        # 随机化Content-Encoding顺序
        encoding_options = [
            "gzip, deflate, br",
            "br, gzip, deflate",
            "deflate, gzip, br"
        ]
        accept_encoding = random.choice(encoding_options)
        
        # 创建真实浏览器指纹 - sec-ch 系列标头
        platforms = ["Windows", "macOS", "Linux", "Android"]
        platform = random.choice(platforms)
        
        # 随机Chrome版本
        chrome_version = random.randint(100, 130)
        
        # 生成随机的Cache-Control值
        cache_control_options = [
            f"max-age={random.randint(0, 300)}",
            "no-cache",
            "max-age=0"
        ]
        cache_control = random.choice(cache_control_options)
        
        # 随机化DNT (Do Not Track)
        dnt = random.choice(["0", "1"])
        
        # 美国时区和地区设置
        us_timezones = ["EST", "CST", "MST", "PST"]
        timezone = random.choice(us_timezones)
        
        # 添加更多随机化的头信息
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": accept_language,
            "Accept-Encoding": accept_encoding,
            "Connection": "keep-alive",
            "Cache-Control": cache_control,
            "DNT": dnt,
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
            "User-Agent": user_agent,
            # SEC-CH 系列标头 - 模拟真实浏览器
            "Sec-Ch-Ua": f'"Chromium";v="{chrome_version}", "Google Chrome";v="{chrome_version}"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": f'"{platform}"',
            # 增强语言和地区设置，确保获取英文内容
            "Content-Language": "en-US",
            "X-Forwarded-For": f"18.{random.randint(200, 250)}.{random.randint(1, 254)}.{random.randint(1, 254)}", # 模拟美国AWS IP
            "X-Country": "US",
            "X-Geo-Country": "US",
            "X-Locale": "en_US",
            "X-Language": "en-US",
            "X-TimeZone": timezone,
            "X-Currency": "USD",
            "Country": "US"
        }
        
        # 随机添加一些额外的头信息，增加多样性
        if random.random() > 0.7:
            headers["Referer"] = "https://www.google.com/"
        
        if random.random() > 0.8:
            headers["X-Requested-With"] = "XMLHttpRequest"
            
        if random.random() > 0.9:
            viewport_widths = [1920, 1440, 1366, 1280]
            viewport_heights = [1080, 900, 768, 720]
            headers["Viewport-Width"] = str(random.choice(viewport_widths))
            headers["Viewport-Height"] = str(random.choice(viewport_heights))
            
        # 添加随机生成的标头，进一步混淆指纹
        # 生成随机字符串作为Header名称和值，更加随机化
        random_key = f"X-{self.md5_encrypt(str(time.time()))[:8]}"
        random_value = self.md5_encrypt(str(random.randint(1000, 9999)))[:10]
        headers[random_key] = random_value
        
        return headers
        
    def save_cookies_to_file(self):
        """将当前会话的cookies保存到文件"""
        try:
            with open(self.cookie_file, 'wb') as f:
                cookies_dict = {
                    'cookies': self.session.cookies,
                    'timestamp': time.time()
                }
                pickle.dump(cookies_dict, f)
            print(f"Cookies已保存到文件: {self.cookie_file}")
            return True
        except Exception as e:
            print(f"保存cookies到文件时出错: {str(e)}")
            return False
            
    def load_cookies_from_file(self):
        """从文件加载cookies到当前会话"""
        try:
            if not os.path.exists(self.cookie_file):
                print(f"Cookies文件不存在: {self.cookie_file}")
                return False
                
            with open(self.cookie_file, 'rb') as f:
                cookies_dict = pickle.load(f)
                
            # 检查cookies是否过期（24小时）
            if time.time() - cookies_dict['timestamp'] > 24 * 60 * 60:
                print("Cookies已过期，需要重新获取")
                # 删除旧的cookie文件
                if os.path.exists(self.cookie_file):
                    os.remove(self.cookie_file)
                    print(f"已删除过期Cookies文件: {self.cookie_file}")
                return False
                
            # 加载cookies到会话
            self.session.cookies = cookies_dict['cookies']
            print("已从文件加载cookies")
            
            # 测试cookies是否有效
            if not self.test_cookies():
                print("加载的cookies无效，需要重新获取")
                # 删除无效的cookie文件
                if os.path.exists(self.cookie_file):
                    os.remove(self.cookie_file)
                    print(f"已删除无效Cookies文件: {self.cookie_file}")
                return False
                
            return True
        except Exception as e:
            print(f"从文件加载cookies时出错: {str(e)}")
            return False

    def get_proxy_info(self, proxy):
        """返回代理信息用于日志记录，不包含敏感凭据"""
        if not proxy:
            return "无代理"
        
        proxy_str = proxy.get('http', '')
        # 移除可能的用户名和密码
        if '@' in proxy_str:
            proxy_str = 'http://' + proxy_str.split('@')[1]
        return proxy_str

    def solve_captcha(self, response):
        """解决亚马逊验证码并返回新的响应"""
        # 先检查是否是"点击继续购物"类型的验证码
        if "Click the button below to continue shopping" in response.text:
            print("检测到'点击继续购物'类型验证码")
            # 获取当前使用的代理，用于暂停
            current_proxy = self.session.proxies.get('http') if hasattr(self.session, 'proxies') else None
            # 暂停当前代理（只有点击继续购物验证码才暂停）
            self.suspend_proxy(current_proxy)
            return self.handle_click_continue_captcha_with_requests(response)
            
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 获取验证码图片URL
        img_element = soup.select_one(".a-spacing-double-large .a-section form .a-spacing-large .a-text-center img")
        if not img_element:
            print("未找到验证码图片")
            # This function uses requests, not selenium, so _capture_screenshot cannot be called here.
            return None
            
        img_url = img_element.get('src')
        
        # 获取hidden表单字段
        amzn = soup.select_one("input[name='amzn']").get('value') if soup.select_one("input[name='amzn']") else None
        amzn_r = soup.select_one("input[name='amzn-r']").get('value') if soup.select_one("input[name='amzn-r']") else None
        
        if not amzn or not amzn_r:
            print("未找到验证表单值")
            # This function uses requests, not selenium, so _capture_screenshot cannot be called here.
            return None
            
        # 使用amazoncaptcha解决验证码
        try:
            captcha = AmazonCaptcha.fromlink(img_url)
            solution = captcha.solve()
            print(f"验证码已解决: {solution}")
            
            # 构建验证URL
            valid_url = f"https://www.amazon.com/errors/validateCaptcha?amzn={amzn}&amzn-r={amzn_r}&field-keywords={solution}"
            
            # 发送验证请求
            headers = self.Headers()
            proxy = self.get_available_proxy()
            
            validation_response = self.session.get(valid_url, headers=headers, proxies=proxy, timeout=10)
            
            if validation_response.status_code == 200:
                print("验证码验证成功")
                # 在验证成功后，检查返回的页面是否真的是产品页面
                if "productTitle" in validation_response.text or "product-title" in validation_response.text:
                    return validation_response
                else:
                    print("验证后未获取到产品页面")
                    return None
            else:
                print(f"验证码验证失败")
                return None
                
        except Exception as e:
            print(f"验证码解决过程中出错: {str(e)}")
            # This function uses requests, not selenium, so _capture_screenshot cannot be called here.
            return None
            
    def handle_click_continue_captcha_with_requests(self, response):
        """使用requests处理'点击继续购物'验证码，将代理加入冷静期"""
        try:
            # 获取当前使用的代理，用于设置冷静期
            current_proxy = self.session.proxies.get('http') if hasattr(self.session, 'proxies') else None
            
            # 保存验证码页面用于调试
            captcha_filename = f"captcha_click_continue_L0_{int(time.time())}.html"
            with open(captcha_filename, "wb") as f:
                f.write(response.content)
            print(f"'点击继续购物'验证码页面已保存至 {captcha_filename}")
            
            # 将代理加入冷静期
            if current_proxy:
                self.suspend_proxy(current_proxy)
                self.handle_proxy_failure(current_proxy, permanent=False)
                print(f"检测到'点击继续购物'验证码，已将代理 {current_proxy} 放入冷静期")
            
            # 直接返回None，不尝试解决验证码或更新Cookie
            return None
        except Exception as e:
            print(f"处理'点击继续购物'验证码时出错: {str(e)}")
            return None
            
    def suspend_proxy(self, proxy):
        """暂时禁用一个代理10分钟 - 对于123Proxy跳过暂停"""
        if not proxy:
            return

        # 对于123Proxy隧道代理，不暂停代理
        # 因为每次请求都会自动分配不同的IP，暂停没有意义
        if self.use_123proxy:
            print("123Proxy隧道代理每次请求都是不同IP，跳过暂停")
            return

        proxy_str = proxy if isinstance(proxy, str) else (proxy.get('http') or proxy.get('https'))
        if not proxy_str:
            return

        # 记录暂停时间（仅用于其他类型的代理）
        suspend_until = time.time() + 600  # 暂停10分钟
        self.suspended_proxies[proxy_str] = suspend_until

        print(f"代理 {proxy_str} 因触发'点击继续购物'验证码被暂停使用10分钟")
    
    def get_available_proxy(self):
        """获取可用代理 - 智能代理选择系统优先"""
        if not self.proxies:
            return None

        # 优先使用智能代理选择系统
        if self.use_smart_proxy:
            return self.get_smart_proxy()

        # 对于123Proxy隧道代理，使用队列系统获取不同的代理配置
        if self.use_123proxy:
            try:
                from queue import Empty
                if not self.proxy_queue_123.empty():
                    # 从队列中获取一个代理配置
                    proxy = self.proxy_queue_123.get_nowait()
                    # 异步补充队列
                    threading.Thread(target=lambda: self.proxy_queue_123.put(self.get_new_123proxy())).start()
                    return proxy
                else:
                    # 队列为空，获取新代理配置
                    new_proxy = self.get_new_123proxy()
                    # 异步补充队列
                    threading.Thread(target=lambda: self.proxy_queue_123.put(self.get_new_123proxy())).start()
                    return new_proxy
            except Empty:
                print("123Proxy代理队列为空，直接获取新代理")
                return self.get_123proxy_address(force_refresh=True)

        # 原有的代理选择逻辑（用于其他类型的代理）
        # 首先清理过期的暂停代理
        current_time = time.time()
        expired_proxies = [p for p, t in self.suspended_proxies.items() if t <= current_time]
        for proxy in expired_proxies:
            self.suspended_proxies.pop(proxy)
            print(f"代理 {proxy} 恢复可用")

        # 过滤掉被暂停的代理
        active_proxies = []
        for proxy in self.proxies:
            proxy_str = proxy if isinstance(proxy, str) else (proxy.get('http') or proxy.get('https', ''))
            if proxy_str not in self.suspended_proxies:
                active_proxies.append(proxy)

        if not active_proxies:
            print("警告: 没有可用的代理，使用无代理模式")
            return None

        return random.choice(active_proxies)
    
    def setup_selenium_browser(self):
        """设置并初始化Selenium浏览器，添加SOCKS5代理支持"""
        try:
            print("初始化Selenium浏览器...")
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            # 禁用无头模式，启用可视化浏览器
            # chrome_options.add_argument('--headless=new')
            # 使用真实的Chrome用户代理
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
            
            # 设置语言和地区，只使用英语
            chrome_options.add_argument('--lang=en-US,en;q=0.9')
            
            # 禁用自动化控制特征
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 添加SOCKS5代理支持
            if self.proxies:
                # 随机选择一个代理
                proxy = random.choice(self.proxies)
                proxy_str = self.get_proxy_info(proxy)
                
                # 从代理字符串中提取信息
                # 格式为 *****************************:port 或 socks5://username:password@host:port
                try:
                    proxy_url = proxy.get('http', '')
                    # 检查是否包含完整的代理信息
                    if '@' in proxy_url and '://' in proxy_url:
                        protocol = proxy_url.split('://')[0]
                        auth_host_port = proxy_url.split('://')[1]
                        
                        if protocol.lower() == 'socks5':
                            auth_part = auth_host_port.split('@')[0]
                            host_port = auth_host_port.split('@')[1]
                            
                            username, password = auth_part.split(':')
                            host, port = host_port.split(':')
                            
                            # 导入代理认证插件创建函数
                            try:
                                # 无需再次导入，使用已导入的函数
                                plugin_path = create_proxy_auth_extension(
                                    proxy_host=host,
                                    proxy_port=port,
                                    proxy_username=username,
                                    proxy_password=password
                                )
                                chrome_options.add_extension(plugin_path)
                                print(f"已添加SOCKS5代理认证插件，代理地址: {host}:{port}")
                            except NameError:
                                # 如果导入失败，回退到基本代理设置
                                print("无法使用代理认证插件，使用基本代理设置")
                                chrome_options.add_argument(f'--proxy-server=socks5://{host}:{port}')
                                print(f"为Selenium设置SOCKS5代理: {host}:{port}")
                                print("警告: 代理认证可能无法生效，Cookie获取可能会失败")
                        else:
                            print(f"只支持SOCKS5代理，当前代理类型: {protocol}")
                    else:
                        print("代理格式不正确，无法为Selenium设置代理")
                except Exception as proxy_e:
                    print(f"解析代理信息出错: {str(proxy_e)}")
            
            # 创建webdriver
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 设置执行CDP命令绕过检测
            self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                "platform": "Windows"
            })
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    })
                '''
            })
            
            return True
        except Exception as e:
            print(f"设置Selenium浏览器时出错: {str(e)}")
            return False

    def close_selenium_browser(self):
        """关闭Selenium浏览器"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            print("Selenium浏览器已关闭")

    def get_keepa_info_with_selenium(self, asin):
        """
        使用Selenium从keepa.com获取ASIN的历史最高价和断货时间
        :param asin: Amazon产品的ASIN码
        :return: 包含历史最高价和断货时间的字典
        """
        result = {
            "historical_highest_price": None,
            "out_of_stock_within_month": 0
        }
        
        if not self.setup_selenium_browser():
            print("无法设置Selenium浏览器，返回默认结果")
            return result
            
        try:
            # 直接URL产品页面
            product_url = f"https://keepa.com/#!product/1-{asin}"
            print(f"Selenium访问Keepa URL: {product_url}")
            
            # 访问网页
            self.driver.get(product_url)
            
            # 等待页面加载，最多等待30秒
            print("等待页面加载...")
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 延迟一些时间以确保JavaScript完全执行
            time.sleep(10)
            
            # 保存页面截图用于调试
            # self.driver.save_screenshot(f"keepa_selenium_{asin}.png")
            print(f"已保存页面截图到 keepa_selenium_{asin}.png")
            
            # 保存页面源代码
            page_source = self.driver.page_source
            with open(f"keepa_selenium_{asin}.html", "w", encoding="utf-8") as f:
                f.write(page_source)
                print(f"已保存Keepa HTML到 keepa_selenium_{asin}.html")
            
            # 用BeautifulSoup解析HTML
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 打印页面标题
            title = soup.find('title')
            if title:
                print(f"页面标题: {title.text}")
            # 等待跟踪表格出现
            try:
                print("查找历史最高价信息...")
                # 尝试寻找历史最高价
                highest_price_elements = soup.find_all('tr', class_='tracking__suggestion-item')
                for elem in highest_price_elements:
                    if elem.find('td') and '历史最高' in elem.find('td').text:
                        price_elem = elem.find_all('td')[-1]
                        if price_elem:
                            price_text = price_elem.text.strip()
                            price_match = re.search(r'\$\s*([\d,.]+)', price_text)
                            if price_match:
                                result["historical_highest_price"] = float(price_match.group(1).replace(',', ''))
                                print(f"找到历史最高价: ${result['historical_highest_price']}")
                                break
                
                # 如果在表格中找不到，尝试在statsTable中寻找
                if result["historical_highest_price"] is None:
                    print("在statsTable中查找历史最高价...")
                    stats_table = soup.find('table', id='statsTable')
                    if stats_table:
                        highest_rows = stats_table.find_all('tr')
                        for row in highest_rows:
                            if row.find('td', class_='statsRow1') and '最高' in row.find('td', class_='statsRow1').text:
                                price_cells = row.find_all('td', class_='statsRow3')
                                if price_cells and len(price_cells) > 0:
                                    price_text = price_cells[0].text.strip()
                                    price_match = re.search(r'\$\s*([\d,.]+)', price_text)
                                    if price_match:
                                        result["historical_highest_price"] = float(price_match.group(1).replace(',', ''))
                                        print(f"从statsTable找到历史最高价: ${result['historical_highest_price']}")
                                        break
                
                # 检查一个月内是否断货
                print("查找断货信息...")
                stats_table = soup.find('table', id='statsTable')
                if stats_table:
                    current_rows = stats_table.find_all('tr')
                    for row in current_rows:
                        if row.find('td', class_='statsRow1') and '目前' in row.find('td', class_='statsRow1').text:
                            date_span = row.find('span', class_='statsDate')
                            if date_span and '天前' in date_span.text:
                                days_match = re.search(r'(\d+)\s*天前', date_span.text)
                                if days_match and int(days_match.group(1)) < 30:
                                    no_stock_elem = row.find('span', style='font-size:11px;')
                                    if no_stock_elem and '无货' in no_stock_elem.text:
                                        result["out_of_stock_within_month"] = 1
                                        print(f"发现产品在一个月内断货")
                                        break
                
                # 直接使用JavaScript提取数据
                if result["historical_highest_price"] is None:
                    print("尝试使用JavaScript提取数据...")
                    # 尝试执行JavaScript提取数据
                    try:
                        # 尝试获取历史最高价
                        highest_price_js = self.driver.execute_script("""
                            // 尝试从页面中查找历史最高价
                            var highestPriceElement = document.querySelector('tr.tracking__suggestion-item td:first-child');
                            var priceElement = null;
                            var rows = document.querySelectorAll('tr.tracking__suggestion-item');
                            var highestPrice = null;
                            
                            for(var i=0; i<rows.length; i++) {
                                var firstCell = rows[i].querySelector('td:first-child');
                                if(firstCell && firstCell.textContent.includes('历史最高')) {
                                    var priceCell = rows[i].querySelector('td:last-child');
                                    if(priceCell) {
                                        var priceText = priceCell.textContent.trim();
                                        var priceMatcher = priceText.match(/\\$(\\d+(?:\\.\\d+)?)/);
                                        if(priceMatcher) {
                                            highestPrice = parseFloat(priceMatcher[1]);
                                            break;
                                        }
                                    }
                                }
                            }
                            
                            return highestPrice;
                        """)
                        
                        if highest_price_js:
                            result["historical_highest_price"] = float(highest_price_js)
                            print(f"通过JavaScript找到历史最高价: ${result['historical_highest_price']}")
                        
                        # 尝试判断断货状态
                        out_of_stock_js = self.driver.execute_script("""
                            var stockTable = document.getElementById('statsTable');
                            if(!stockTable) return 0;
                            
                            var rows = stockTable.querySelectorAll('tr');
                            for(var i=0; i<rows.length; i++) {
                                var firstCell = rows[i].querySelector('td.statsRow1');
                                if(firstCell && firstCell.textContent.includes('目前')) {
                                    var dateSpan = rows[i].querySelector('span.statsDate');
                                    if(dateSpan && dateSpan.textContent.includes('天前')) {
                                        var days = parseInt(dateSpan.textContent.match(/(\\d+)\\s*天前/)[1]);
                                        if(days < 30) {
                                            var noStockElement = rows[i].querySelector('span[style*="font-size:11px"]');
                                            if(noStockElement && noStockElement.textContent.includes('无货')) {
                                                return 1;
                                            }
                                        }
                                    }
                                }
                            }
                            return 0;
                        """)
                        
                        if out_of_stock_js == 1:
                            result["out_of_stock_within_month"] = 1
                            print("通过JavaScript发现产品在一个月内断货")
                    
                    except Exception as js_error:
                        print(f"JavaScript数据提取出错: {str(js_error)}")
            
            except TimeoutException:
                print("等待页面元素超时")
            except Exception as e:
                print(f"在处理页面时出错: {str(e)}")
            
            return result
            
        except Exception as e:
            print(f"Selenium爬取Keepa信息时出错: {str(e)}")
            return result
        finally:
            # 确保关闭浏览器
            self.close_selenium_browser()

    def get_keepa_info(self, asin):
        """
        从keepa.com获取ASIN的历史最高价和断货时间
        :param asin: Amazon产品的ASIN码
        :return: 包含历史最高价和断货时间的字典
        """
        print("尝试使用Selenium获取Keepa信息...")
        return self.get_keepa_info_with_selenium(asin)

    def convert_image_to_base64(self, img_url):
        """将图片URL转换为Base64编码"""
        try:
            # 使用selenium直接获取图片
            if self.driver:
                # 获取图片元素
                img_element = self.driver.find_element(By.CSS_SELECTOR, "img:nth-child(1)")
                if img_element:
                    # 使用JavaScript获取图片为Base64
                    base64_img = self.driver.execute_script("""
                        var img = arguments[0];
                        var canvas = document.createElement('canvas');
                        canvas.width = img.width;
                        canvas.height = img.height;
                        var ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0);
                        return canvas.toDataURL('image/png').replace(/^data:image\\/(png|jpg);base64,/, '');
                    """, img_element)
                    return base64_img
                
            # 如果直接获取失败，尝试通过URL请求图片
            response = requests.get(img_url, timeout=10)
            if response.status_code == 200:
                return base64.b64encode(response.content).decode('utf-8')
            return None
        except Exception as e:
            print(f"转换图片到Base64时出错: {str(e)}")
            return None

    def is_captcha_present(self):
        """检查是否存在验证码页面或点击验证页面，并返回类型"""
        try:
            # 检查传统图片验证码元素
            captcha_elements_xpath = [
                "//input[@id='captchacharacters']",  # 验证码输入框
                "//form[contains(@action, '/errors/validateCaptcha')]",  # 验证码表单
                "//div[contains(text(), 'Enter the characters you see below')]",  # 验证码提示文本
                "//div[contains(text(), 'Type the characters you see')]"  # 另一种验证码提示
            ]
            for element_xpath in captcha_elements_xpath:
                if self.driver.find_elements(By.XPATH, element_xpath):
                    print("检测到传统图片验证码页面")
                    return "image_captcha"

            # 检查"点击继续购物"挑战元素
            # 查找"Click the button below to continue shopping"文本或带有"Continue shopping"文本的按钮
            if self.driver.find_elements(By.XPATH, "//div[contains(text(), 'Click the button below to continue shopping')]") or \
               self.driver.find_elements(By.XPATH, '//button[@class="a-button-text" and contains(text(), "Continue shopping")]'):
                print("检测到'点击继续购物'挑战页面")
                return "click_continue"

            return "none" # 没有找到任何已知类型的验证码
        except Exception as e:
            print(f"检查验证码或点击继续挑战时出错: {str(e)}")
            return "error" # 检测过程中发生错误

    def handle_captcha(self):
        """处理验证码或点击继续挑战"""
        try:
            print("开始处理挑战...")
            
            captcha_type = self.is_captcha_present() # 获取挑战类型

            if captcha_type == "click_continue":
                print("识别到'点击继续购物'挑战，尝试点击按钮...")
                try:
                    continue_shopping_button = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, '//button[@class="a-button-text" and contains(text(), "Continue shopping")]'))
                    )
                    print("找到'Continue shopping'按钮，点击...")
                    continue_shopping_button.click()
                    
                    # 等待页面加载完成
                    WebDriverWait(self.driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                    time.sleep(2) # 增加短暂等待以确保页面稳定
                    
                    # 检查是否仍停留在挑战页面
                    if self.is_captcha_present() != "none": # 重新检查是否仍有任何挑战
                        print("点击'Continue shopping'后仍停留在挑战页面。")
                        return False
                    
                    print("'点击继续购物'挑战处理成功。")
                    return True
                except (TimeoutException, NoSuchElementException, ElementClickInterceptedException) as e:
                    print(f"点击'Continue shopping'按钮时失败或被拦截: {str(e)}")
                    return False # 处理此类型失败
                except Exception as e:
                    print(f"处理'点击继续购物'挑战时发生未知错误: {str(e)}")
                    return False

            elif captcha_type == "image_captcha":
                print("识别到传统图片验证码，尝试解决...")
                
                # 检查是否存在验证码图片
                try:
                    captcha_img = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "img:nth-child(1)")))
                    img_url = captcha_img.get_attribute("src")
                except TimeoutException:
                    print("未找到验证码图片元素。")
                    return False

                # 检查是否存在验证码输入框
                try:
                    captcha_input = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "input#captchacharacters")))
                except TimeoutException:
                    print("未找到验证码输入框。")
                    return False

                # 尝试找到提交按钮
                submit_button = None
                for button_xpath in [
                    "//button[contains(text(), 'Continue shopping')]",
                    "//input[@type='submit']",
                    "//button[@type='submit']"
                ]:
                    try:
                        submit_button = WebDriverWait(self.driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, button_xpath)))
                        if submit_button:
                            break
                    except:
                        continue

                if not submit_button:
                    print("未找到提交验证码的按钮")
                    return False

                print("检测到传统验证码页面，开始使用amazoncaptcha处理...")

                # 使用amazoncaptcha库解决验证码
                try:
                    captcha = AmazonCaptcha.fromlink(img_url)
                    solution = captcha.solve()
                    if solution:
                        print(f"验证码解决方案: {solution}")
                        # 输入验证码
                        captcha_input.clear()
                        captcha_input.send_keys(solution)
                        print("验证码已输入，点击提交...")
                        submit_button.click()
                        
                        # 等待页面加载完成
                        WebDriverWait(self.driver, 10).until(EC.presence_of_element_located((By.XPATH, "//body")))
                        
                        # 检查是否仍在验证码页面
                        if self.is_captcha_present() != "none":
                            print("传统验证码验证失败，仍在验证码页面")
                            return False
                        
                        print("传统验证码处理成功，页面已加载")
                        return True
                    else:
                        print("传统验证码解决失败")
                        return False
                except Exception as e:
                    print(f"使用amazoncaptcha解决验证码时出错: {str(e)}")
                    return False
            
            elif captcha_type == "none":
                print("未检测到任何挑战页面。")
                return True # 没有挑战，所以是"处理成功"

            else: # captcha_type == "error" 或其他未处理类型
                print(f"未能识别或处理挑战类型: {captcha_type}")
                return False

        except Exception as e:
            print(f"处理挑战时发生整体错误: {str(e)}")
            return False

    def update_cookies_with_selenium(self):
        """使用Selenium自动获取新的cookie并更新会话"""
        with self.lock:
            print("使用Selenium更新Cookies...")
            
            # 浏览器重启外循环
            max_browser_restarts = 3
            browser_restart_count = 0
            
            while browser_restart_count < max_browser_restarts:
                if not self.setup_selenium_browser():
                    print("Selenium浏览器设置失败，无法更新cookies")
                    return False
                    
                try:
                    # 访问Amazon美国站点，只添加语言参数到URL
                    amazon_url = "https://www.amazon.com/?language=en_US&country=US"
                    print(f"导航到 {amazon_url}")
                    self.driver.get(amazon_url)
                    
                    # 等待页面加载
                    WebDriverWait(self.driver, 30).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                    time.sleep(2)  # 稳定等待
                    
                    # 检查是否有验证码页面
                    if self.is_captcha_present():
                        print("检测到验证码或挑战页面，尝试处理...")
                        captcha_solved = self.handle_captcha()
                        if not captcha_solved:
                            print("验证码处理失败，尝试刷新页面重试")
                            self.driver.refresh()
                            time.sleep(3)  # 等待刷新
                            
                            # 再次检查验证码
                            if self.is_captcha_present():
                                captcha_solved = self.handle_captcha()
                                if not captcha_solved:
                                    print("再次尝试处理验证码失败，无法继续")
                                    self.close_selenium_browser()
                                    browser_restart_count += 1
                                    print(f"重启浏览器 ({browser_restart_count}/{max_browser_restarts})")
                                    continue
                    
                    # 检查并点击位置选择器
                    # 增加刷新重试机制
                    max_refresh_attempts = 3
                    refresh_attempt = 0
                    
                    while refresh_attempt < max_refresh_attempts:
                        print(f"尝试 #{refresh_attempt+1}/{max_refresh_attempts}")
                        location_selector_found = False
                        zipcode_input_found = False
                        
                        try:
                            # 首先尝试查找位置选择器
                            print(f"查找位置选择器...")
                            location_selector = WebDriverWait(self.driver, 10).until(
                                EC.element_to_be_clickable((By.ID, "nav-global-location-popover-link"))
                            )
                            
                            location_selector_found = True
                            print("找到位置选择器，点击打开位置选择对话框")
                            location_selector.click()
                            time.sleep(2)  # 等待位置对话框加载
                            
                            # 尝试查找邮编输入框
                            print("查找邮编输入框...")
                            try:
                                zipcode_input = WebDriverWait(self.driver, 10).until(
                                    EC.presence_of_element_located((By.ID, "GLUXZipUpdateInput"))
                                )
                                
                                zipcode_input_found = True
                                print("找到邮编输入框，输入美国邮编 98032")
                                zipcode_input.clear()
                                zipcode_input.send_keys("98032")
                                
                                # 点击Apply按钮
                                apply_button = WebDriverWait(self.driver, 10).until(
                                    EC.element_to_be_clickable((By.CSS_SELECTOR, "input[aria-labelledby='GLUXZipUpdate-announce']"))
                                )
                                print("点击Apply按钮")
                                apply_button.click()
                                
                                # 增加等待时间，确保完成按钮有足够时间出现在界面上
                                print("等待邮编更新对话框和完成按钮出现...")
                                time.sleep(5)
                                
                                # 点击Done按钮
                                try:
                                    # 直接尝试查找并点击具有data-action="a-popover-close"属性的元素
                                    print("尝试查找并点击完成按钮...")
                                    
                                    # 使用JavaScript直接查找并点击元素
                                    script = """
                                        var closeElement = document.querySelector('[data-action="a-popover-close"]');
                                        if(closeElement) {
                                            closeElement.click();
                                            return true;
                                        }
                                        return false;
                                    """
                                    result = self.driver.execute_script(script)
                                    
                                    if result:
                                        print("成功点击完成按钮")
                                    else:
                                        print("未找到完成按钮，尝试备用方法")
                                        
                                        # 备用方法1：通过name属性查找按钮
                                        try:
                                            done_button = WebDriverWait(self.driver, 5).until(
                                                EC.element_to_be_clickable((By.NAME, "glowDoneButton"))
                                            )
                                            print("找到glowDoneButton按钮，点击")
                                            done_button.click()
                                        except Exception as e:
                                            print(f"尝试点击glowDoneButton按钮失败: {str(e)}")
                                            
                                            # 备用方法2：通过CSS选择器查找
                                            try:
                                                css_selector = "div.a-popover-footer button.a-button-text"
                                                done_button = WebDriverWait(self.driver, 5).until(
                                                    EC.element_to_be_clickable((By.CSS_SELECTOR, css_selector))
                                                )
                                                print("使用CSS选择器找到按钮，点击")
                                                done_button.click()
                                            except Exception as css_e:
                                                print(f"使用CSS选择器查找按钮失败")
                                
                                except Exception as e:
                                    print(f"点击完成按钮过程中出错: {str(e)}")
                                
                                # 等待页面更新
                                print("等待页面更新...")
                                time.sleep(5)
                                
                                # 邮编设置完成，跳出刷新循环
                                break
                                
                            except Exception as e:
                                print(f"找不到邮编输入框: {str(e)}")
                                # 找到位置选择器但没找到邮编输入框，需要刷新页面
                                refresh_attempt += 1
                                if refresh_attempt < max_refresh_attempts:
                                    print(f"找到位置选择器但没有邮编输入框，刷新页面 ({refresh_attempt}/{max_refresh_attempts}) 并重试")
                                    self.driver.refresh()
                                    time.sleep(3)  # 等待页面刷新
                                else:
                                    print(f"尝试了 {max_refresh_attempts} 次，仍未找到邮编输入框")
                        
                        except Exception as e:
                            # 没找到位置选择器
                            print(f"查找或点击位置选择器失败: {str(e)}")
                            refresh_attempt += 1
                            if refresh_attempt < max_refresh_attempts:
                                print(f"没找到位置选择器，刷新页面 ({refresh_attempt}/{max_refresh_attempts}) 并重试")
                                self.driver.refresh()
                                time.sleep(3)  # 等待页面刷新
                            else:
                                print(f"尝试了 {max_refresh_attempts} 次，仍未找到位置选择器")
                    
                    # 如果刷新3次后仍未找到位置选择器或邮编输入框
                    if not location_selector_found:
                        print(f"经过 {max_refresh_attempts} 次尝试，仍未找到位置选择器")
                        self.close_selenium_browser()
                        browser_restart_count += 1
                        print(f"重启浏览器 ({browser_restart_count}/{max_browser_restarts})")
                        continue
                    
                    if not zipcode_input_found:
                        print(f"经过 {max_refresh_attempts} 次尝试，找到位置选择器但未找到邮编输入框")
                        self.close_selenium_browser()
                        browser_restart_count += 1
                        print(f"重启浏览器 ({browser_restart_count}/{max_browser_restarts})")
                        continue
                    
                    # 获取新的cookies
                    new_cookies = {}
                    for cookie in self.driver.get_cookies():
                        new_cookies[cookie['name']] = cookie['value']
                    
                    # 检查关键cookie是否存在
                    required_cookies = ['session-id', 'session-token', 'ubid-main']
                    missing_cookies = [c for c in required_cookies if c not in new_cookies]
                    
                    if missing_cookies:
                        print(f"警告: 缺少关键cookie: {missing_cookies}")
                        # 尝试通过刷新页面再次获取
                        self.driver.refresh()
                        time.sleep(5)
                        
                        # 重新获取cookies
                        new_cookies = {}
                        for cookie in self.driver.get_cookies():
                            new_cookies[cookie['name']] = cookie['value']
                        
                        # 再次检查
                        missing_cookies = [c for c in required_cookies if c not in new_cookies]
                        if missing_cookies:
                            print(f"仍然缺少关键cookie: {missing_cookies}，但将使用可用的cookies")
                    
                    # 确保语言相关cookies存在
                    if 'i18n-prefs' not in new_cookies:
                        new_cookies['i18n-prefs'] = 'USD'
                    if 'lc-main' not in new_cookies:
                        new_cookies['lc-main'] = 'en_US'
                    
                    # 打印获取到的所有cookie
                    print("成功获取到新cookie:")
                    for name, value in new_cookies.items():
                        print(f"{name}: {value}")
                    
                    # 清空当前session的所有cookies，确保从一个干净的状态开始添加新cookies
                    self.session.cookies.clear()
                    
                    # 更新session的cookies
                    for name, value in new_cookies.items():
                        self.session.cookies.set(name, value, domain='.amazon.com')
                    
                    print("Session cookies已更新")
                    return True
                    
                except Exception as e:
                    print(f"使用Selenium更新cookies时出错: {str(e)}")
                    self.close_selenium_browser()
                    browser_restart_count += 1
                    print(f"重启浏览器 ({browser_restart_count}/{max_browser_restarts})")
                    if browser_restart_count >= max_browser_restarts:
                        return False
                finally:
                    # 确保关闭Selenium浏览器
                    self.close_selenium_browser()
            
            return False  # 如果所有尝试都失败

    def test_cookies(self):
        """测试当前cookies是否有效"""
        test_url = "https://www.amazon.com/robots.txt"
        headers = self.Headers()
        proxy = random.choice(self.proxies) if self.proxies else None
        
        try:
            print("测试cookies是否有效...")
            response = self.session.get(test_url, headers=headers, proxies=proxy, timeout=10)
            
            if response.status_code == 200:
                print("Cookies测试成功")
                return True
            else:
                print(f"Cookies测试失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"测试cookies时出错: {str(e)}")
            return False

    def update_us_cookies(self):
        """更新访问亚马逊US站的cookies信息"""
        print("更新美国地区cookies...")
        
        # 首先尝试使用请求方式获取cookies
        if self.update_cookies_with_requests():
            print("使用请求方式更新cookies成功")
            
            # 测试cookies是否有效并保存到文件
            if self.test_cookies():
                self.save_cookies_to_file()
                return True
                
        # 请求方式失败，尝试使用Selenium获取新cookies
        if self.update_cookies_with_selenium():
            print("使用Selenium更新cookies成功")
            
            # 测试cookies是否有效并保存到文件
            if self.test_cookies():
                self.save_cookies_to_file()
                return True
        
        print("更新cookies失败")
        return False

    def get_amazon_product_info(self, asin):
        """
        获取Amazon产品信息
        :param asin: Amazon产品的ASIN码
        :return: 包含产品信息的字典
        """
        # 优化URL参数，确保使用英文页面和美国区域
        url = f"https://www.amazon.com/dp/{asin}?language=en_US&zipcode=10001&LanguagePreference=en_US&country=US&ie=UTF8&ref_=nav_ya_signin&lc=en_US"
        retries = self.max_retries  # 使用配置的重试次数
        timeout_count = 0  # 初始化超时计数器
        
        # 初始化结果字典
        result = {
            "asin": asin,
            "url": url,
            "title": None,
            "brand": None,
            "brand_type": None,
            "dimensions": None,
            "weight": None,
            "rating": None,
            "rating_count": None,
            "sales_rank": [],
            "image_url": None,
            "has_variants": False,
            "is_out_of_stock": False,
            "is_unique_brand": False,  # 新增字段：品牌是否唯一
            "html_content": ""  # 保存HTML内容用于后续分析
        }
        
        # 尝试多次使用智能代理选择
        for attempt in range(retries):
            # 每次尝试都重新生成随机头部和使用智能代理选择
            headers = self.Headers()
            proxy = self.get_available_proxy()  # 使用智能代理选择
            proxy_info = self.get_proxy_info(proxy)

            # 记录请求开始时间
            start_time = time.time()

            # 对于智能代理系统，跳过随机延迟，因为每次请求都可能是不同IP
            if not self.use_smart_proxy and not self.use_123proxy:
                delay = random.uniform(1.0, 2.0)
                print(f"ASIN {asin} 添加随机延迟: {delay:.2f}秒")
                time.sleep(delay)

            print(f"ASIN {asin} 尝试 #{attempt + 1}/{retries} 使用代理: {proxy_info}")

            try:
                # 先检查代理是否有效
                if proxy and not self.check_proxy(proxy):
                    if self.use_123proxy:
                        print(f"ASIN {asin}: 123Proxy代理暂时不可用，继续尝试")
                        # 对于123Proxy，不移除代理，继续使用
                    else:
                        print(f"ASIN {asin}: 代理 {proxy_info} 无效，将移除并尝试新代理")
                        self.handle_proxy_failure(proxy, permanent=True)
                    continue
                
                # 设置cookies强制使用英文
                self.session.cookies.set('i18n-prefs', 'USD', domain='.amazon.com', path='/')
                self.session.cookies.set('lc-main', 'en_US', domain='.amazon.com', path='/')
                self.session.cookies.set('session-id-time', str(int(time.time())), domain='.amazon.com', path='/')
                self.session.cookies.set('amazon-shopping-pref', 'language=en_US&country=US', domain='.amazon.com', path='/')
                
                # 执行请求，传递cookies参数 - 增加超时时间以适应代理响应时间
                response = self.session.get(url, headers=headers, proxies=proxy, timeout=20)
                
                # 处理各种HTTP状态码
                if response.status_code == 200:
                    if "Click the button below to continue shopping" in response.text:
                        if self.use_123proxy:
                            # 123Proxy会自动分配新IP，静默重试
                            pass
                        else:
                            print(f"ASIN {asin} 遇到点击继续购物验证码，将代理放入冷静期")
                            # 暂停当前代理（仅用于其他类型的代理）
                            if proxy:
                                self.suspend_proxy(proxy)
                                self.handle_proxy_failure(proxy, permanent=False)
                                print(f"已将代理放入冷静期，尝试使用新代理")
                        continue
                    elif "Type the characters you see in this image" in response.text or "captcha" in response.text.lower():
                        print(f"ASIN {asin} 遇到验证码，尝试解决")
                        
                        # 尝试解决验证码
                        captcha_response = self.solve_captcha(response)
                        if captcha_response:
                            # 使用验证后的响应替换原响应
                            response = captcha_response
                        else:
                            print(f"ASIN {asin} 验证码解决失败，尝试通过Selenium更新Cookie")
                            # 尝试通过Selenium更新Cookie
                            if self.update_cookies_with_selenium():
                                print(f"ASIN {asin} Selenium更新Cookie成功，重试请求")
                                continue  # 使用新Cookie重试
                            else:
                                print(f"ASIN {asin} Selenium更新Cookie失败，尝试新代理")
                                time.sleep(random.uniform(2, 5))
                                continue
                    
                    # 检查是否需要登录或Cookie过期
                    if "Sign in for the best experience" in response.text or "To discuss automated access to Amazon data please contact" in response.text:
                        print(f"ASIN {asin} Cookie可能已过期，尝试通过Selenium更新Cookie")
                        if self.update_cookies_with_selenium():
                            print(f"ASIN {asin} Selenium更新Cookie成功，重试请求")
                            continue  # 使用新Cookie重试
                    
                    # 保存HTML内容以便后续分析
                    result["html_content"] = response.text
                    
                    # 解析HTML
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 改进的语言检测逻辑，检查中文字符
                    title = soup.find('title')
                    if title:
                        title_text = title.text
                        
                        # 使用更准确的方法检测中文字符
                        def contains_chinese(text):
                            chinese_range = re.compile(u'[\u4e00-\u9fff]')
                            return bool(chinese_range.search(text))
                        
                        if contains_chinese(title_text):
                            print(f"ASIN {asin}: 获取到中文页面，尝试强制转换为英文")
                            # 先尝试使用新函数强制转换英文页面
                            english_response = self.force_english_page(response)
                            if english_response:
                                response = english_response
                                soup = BeautifulSoup(response.content, 'html.parser')
                                print(f"ASIN {asin}: 成功转换为英文页面")
                            else:
                                print(f"ASIN {asin}: 强制转换失败，尝试更新Cookie")
                                # 尝试通过Selenium更新Cookie获取英文页面
                                if self.update_cookies_with_selenium():
                                    print(f"ASIN {asin} Selenium更新Cookie成功，重试请求")
                                    continue  # 使用新Cookie重试
                                else:
                                    # 如果更新失败，则尝试用新代理
                                    print(f"ASIN {asin} 更新Cookie失败，尝试新代理")
                                    if proxy:
                                        self.handle_proxy_failure(proxy, permanent=False)
                                    continue
                        else:
                            print("确认页面为英文")
                    
                    # 检查是否断货
                    out_of_stock_elem = soup.find('div', id='outOfStock')
                    if out_of_stock_elem and "Currently unavailable" in out_of_stock_elem.text:
                        print(f"ASIN {asin}: 产品断货，符合条件，继续处理")
                        result["is_out_of_stock"] = True
                    else:
                        print(f"ASIN {asin}: 产品有货，不符合要求，跳过")
                        result["is_out_of_stock"] = False
                        return result
                    
                    # 检查是否有变体
                    variant_elements = soup.find_all('div', id=lambda x: x and x.startswith('dimension-slot-info-'))
                    if len(variant_elements) > 1:
                        print(f"ASIN {asin}: 产品有多个变体，跳过")
                        result["has_variants"] = True
                        return result
                    
                    # 提取产品标题
                    title_elem = soup.select_one("#productTitle")
                    if title_elem:
                        result["title"] = title_elem.text.strip()
                    
                    # 提取品牌和品牌类型
                    brand_element = soup.find('a', id='bylineInfo')
                    if brand_element:
                        brand_text = brand_element.text.strip()
                        if "Visit the" in brand_text:
                            result["brand"] = brand_text.replace("Visit the", "").replace("Store", "").strip()
                            result["brand_type"] = "Visit the"
                        else:
                            result["brand"] = brand_text.replace("Brand:", "").strip()
                            result["brand_type"] = "Brand:"
                        print(f"找到品牌: {result['brand']} (类型: {result['brand_type']})")
                        
                        # 检查品牌是否唯一
                        if result["brand"]:
                            # 在亚马逊搜索这个品牌
                            time.sleep(random.uniform(1, 3))  # 避免请求过快
                            found_asins = self.search_brand_on_amazon(result["brand"])
                            
                            # 过滤掉原始ASIN
                            other_asins = [a for a in found_asins if a != asin]
                            
                            if not other_asins:
                                print(f"ASIN {asin}: 品牌 {result['brand']} 没有其他产品，认为是唯一的")
                                result["is_unique_brand"] = True
                            else:
                                print(f"ASIN {asin}: 找到品牌 {result['brand']} 的其他 {len(other_asins)} 个产品，检查是否同品牌...")
                                
                                # 改为逐个检查，找到一个相同的就立即停止
                                result["is_unique_brand"] = True  # 初始假设为唯一
                                
                                # 记录查询其他品牌时的超时次数
                                timeout_count = 0
                                max_timeouts = 3
                                
                                # 最多检查20个其他ASIN，减少不必要的请求
                                check_limit = min(20, len(other_asins))
                                for i in range(check_limit):
                                    check_asin = other_asins[i]
                                    try:
                                        brand_type, brand = self.get_brand(check_asin)
                                        if brand and brand.lower() == result["brand"].lower():
                                            print(f"ASIN {asin}: 发现相同品牌 {brand} 的其他产品 {check_asin}，不是唯一的")
                                            result["is_unique_brand"] = False
                                            print(f"ASIN {asin}: 品牌 {result['brand']} 不是唯一的，返回结果并跳过后续处理")
                                            return result  # 如果找到相同品牌，立即返回
                                    except requests.exceptions.Timeout:
                                        print(f"ASIN {asin}: 检查品牌ASIN {check_asin}时超时")
                                        timeout_count += 1
                                        if timeout_count >= max_timeouts:
                                            print(f"ASIN {asin}: 检查品牌时超时次数达到 {max_timeouts} 次，记录到超时表格")
                                            # 将原始的主ASIN添加到超时表格
                                            self.save_timeout_asin(asin, result['brand'])
                                            # 因为超时太多，直接返回结果并终止后续处理
                                            result["is_unique_brand"] = False
                                            return result
                                        continue
                                    except Exception as e:
                                        print(f"检查ASIN {check_asin}品牌时出错: {str(e)}")
                                        continue  # 出错时继续检查下一个
                                
                                # 检查完所有限定的ASIN都没有找到相同品牌
                                print(f"ASIN {asin}: 检查了 {check_limit} 个产品，确认品牌 {result['brand']} 是唯一的")
                    
                    # 提取主图URL
                    try:
                        # 尝试从页面上的JavaScript数据中提取图片URL
                        image_script = None
                        for script in soup.find_all('script', type='text/javascript'):
                            if 'ImageBlockATF' in script.text and 'colorImages' in script.text:
                                image_script = script.text
                                break
                        
                        if image_script:
                            # 提取colorImages部分
                            match = re.search(r'colorImages\':\s*{\s*\'initial\':\s*(\[.*?\])', image_script, re.DOTALL)
                            if match:
                                image_data_str = match.group(1)
                                # 清理JavaScript对象使其成为有效的JSON
                                image_data_str = re.sub(r'(\w+):', r'"\1":', image_data_str)
                                image_data_str = re.sub(r'\'', '"', image_data_str)
                                
                                try:
                                    image_data = json.loads(image_data_str)
                                    if image_data and len(image_data) > 0 and 'hiRes' in image_data[0]:
                                        result['image_url'] = image_data[0]['hiRes']
                                    elif image_data and len(image_data) > 0 and 'large' in image_data[0]:
                                        result['image_url'] = image_data[0]['large']
                                except json.JSONDecodeError:
                                    # 尝试提取直接URL
                                    img_url_match = re.search(r'"hiRes":\s*"(https://[^"]+)"', image_script)
                                    if img_url_match:
                                        result['image_url'] = img_url_match.group(1)
                                    else:
                                        img_url_match = re.search(r'"large":\s*"(https://[^"]+)"', image_script)
                                        if img_url_match:
                                            result['image_url'] = img_url_match.group(1)
                        
                        # 如果上面方法失败，尝试从图片标签获取
                        if not result['image_url']:
                            main_image = soup.find('img', id='landingImage') or soup.find('img', id='imgBlkFront')
                            if main_image and 'src' in main_image.attrs:
                                result['image_url'] = main_image['src']
                            elif main_image and 'data-old-hires' in main_image.attrs:
                                result['image_url'] = main_image['data-old-hires']
                        
                    except Exception as e:
                        print(f"提取图片URL时出错: {str(e)}")
                
                    # 提取尺寸信息
                    for tr in soup.find_all('tr'):
                        th = tr.find('th')
                        if th and ('Dimensions' in th.text or 'Product Dimensions' in th.text):
                            dimensions_elem = tr.find('td')
                            if dimensions_elem:
                                result["dimensions"] = dimensions_elem.text.strip()
                            break
                        
                    # 提取重量信息
                    for tr in soup.find_all('tr'):
                        th = tr.find('th')
                        if th and 'Weight' in th.text:
                            weight_elem = tr.find('td')
                            if weight_elem:
                                result["weight"] = weight_elem.text.strip()
                            break
                        
                    # 提取评分
                    rating_elem = soup.select_one("span[data-hook='rating-out-of-text'], span.a-icon-alt")
                    if rating_elem:
                        rating_text = rating_elem.text
                        rating_match = re.search(r'([\d.]+) out of', rating_text)
                        if rating_match:
                            result["rating"] = float(rating_match.group(1))
                        else:
                            # 尝试其他可能的格式
                            rating_match = re.search(r'([\d.]+)', rating_text)
                            if rating_match:
                                result["rating"] = float(rating_match.group(1))
                    
                    # 如果上面的方法没找到评分，尝试其他选择器
                    if result["rating"] is None:
                        for span in soup.find_all('span'):
                            if span.get('aria-label') and 'out of 5 stars' in span.get('aria-label'):
                                rating_text = span.get('aria-label')
                                rating_match = re.search(r'([\d.]+) out of', rating_text)
                                if rating_match:
                                    result["rating"] = float(rating_match.group(1))
                                    break
                    
                    # 提取评论数量
                    reviews_elem = soup.select_one("span#acrCustomerReviewText")
                    if reviews_elem:
                        reviews_text = reviews_elem.text
                        reviews_match = re.search(r'([\d,]+)', reviews_text)
                        if reviews_match:
                            result["rating_count"] = int(reviews_match.group(1).replace(',', ''))
                    
                    # 如果上面的方法没找到评论数，尝试其他选择器
                    if result["rating_count"] is None:
                        for a in soup.find_all('a', {'id': 'acrCustomerReviewLink'}):
                            reviews_text = a.text
                            reviews_match = re.search(r'([\d,]+)', reviews_text)
                            if reviews_match:
                                result["rating_count"] = int(reviews_match.group(1).replace(',', ''))
                                break
                    
                    # 检查评论数是否在要求范围内
                    if result["rating_count"] is not None:
                        if result["rating_count"] < 20 or result["rating_count"] > 2000:
                            print(f"ASIN {asin}: 评论数 {result['rating_count']} 不在要求范围内(20-2000)，跳过")
                            return result
                    
                    # 提取销售排名
                    rank_section = None
                    for tr in soup.find_all('tr'):
                        th = tr.find('th')
                        if th and ('Best Sellers Rank' in th.text or 'Sellers Rank' in th.text):
                            rank_section = tr.find('td')
                            break
                            
                    if rank_section:
                        # 提取排名文本
                        rank_text = rank_section.text.strip()
                        print(f"销售排名原始文本: {rank_text}")
                        # 提取每个排名
                        rank_matches = re.finditer(r'#([\d,]+) in ([^#\(]+)', rank_text)
                        for match in rank_matches:
                            rank_num = int(match.group(1).replace(',', ''))
                            category = match.group(2).strip()
                            result["sales_rank"].append({
                                "rank": rank_num,
                                "category": category
                            })
                            
                        if result["sales_rank"]:
                            print(f"找到销售排名: {result['sales_rank']}")

                    # 记录代理成功使用
                    response_time = time.time() - start_time
                    self.record_proxy_result(proxy, True, response_time)

                    # 成功获取到产品信息，返回结果
                    return result
                
                elif response.status_code == 503:
                    if self.use_123proxy:
                        print(f"ASIN {asin}: 服务暂时不可用(503)，123Proxy会自动分配新IP，直接重试")
                        return self.get_amazon_product_info(asin, attempt + 1)
                    else:
                        backoff_time = self.calculate_backoff_time(attempt)
                        print(f"ASIN {asin}: 服务暂时不可用(503)，等待 {backoff_time:.2f} 秒后重试")
                        time.sleep(backoff_time)
                        return self.get_amazon_product_info(asin, attempt + 1)
                    
                elif response.status_code == 429:  # Too Many Requests
                    if self.use_123proxy:
                        print(f"ASIN {asin}: 请求过多(429)，123Proxy会自动分配新IP，直接重试")
                        continue
                    else:
                        backoff_time = self.calculate_backoff_time(attempt, base_delay=15)
                        print(f"ASIN {asin}: 请求过多(429)，等待 {backoff_time:.2f} 秒后使用新代理重试")
                        if proxy:
                            self.handle_proxy_failure(proxy, permanent=False)
                        time.sleep(backoff_time)
                        continue
                    
                elif response.status_code == 500:
                    print(f"ASIN {asin}: 服务器错误(500)，直接重试")
                    continue
                else:
                    print(f"ASIN {asin}: 请求失败，状态码 {response.status_code}")
                    if proxy:
                        self.handle_proxy_failure(proxy, permanent=False)
                    backoff_time = self.calculate_backoff_time(attempt, base_delay=3)
                    time.sleep(backoff_time)
                    continue
                    
            except requests.exceptions.ProxyError:
                print(f"ASIN {asin}: 代理连接错误")
                # 记录代理失败
                self.record_proxy_result(proxy, False)
                if proxy:
                    self.handle_proxy_failure(proxy, permanent=False)
                continue  # 直接尝试新代理
            except requests.exceptions.Timeout:
                print(f"ASIN {asin}: 请求超时，可能是代理问题")
                # 记录代理失败
                self.record_proxy_result(proxy, False)
                if proxy:
                    self.handle_proxy_failure(proxy, permanent=False)
                timeout_count += 1
                continue  # 直接尝试新代理
            except requests.exceptions.ConnectionError:
                print(f"ASIN {asin}: 连接错误，可能是代理问题")
                # 记录代理失败
                self.record_proxy_result(proxy, False)
                if proxy:
                    self.handle_proxy_failure(proxy, permanent=False)
                continue  # 直接尝试新代理
            except Exception as e:
                print(f"ASIN {asin}: 未知错误 {str(e)}")
                # 记录代理失败
                self.record_proxy_result(proxy, False)
                backoff_time = self.calculate_backoff_time(attempt, base_delay=2)
                time.sleep(backoff_time)
                continue
            
        # 无法获取产品信息，保存到专门的表格中
        self.add_failed_to_get_info_asin(asin, result.get('brand'), '无法获取产品信息，已达到重试次数上限')
        return result  # 返回可能不完整的结果

    def get_complete_product_info(self, asin):
        """
        获取产品信息
        :param asin: Amazon产品的ASIN码
        :return: 包含产品信息的字典
        """
        # 获取Amazon产品信息
        return self.get_amazon_product_info(asin)

    def remove_invalid_proxy(self, invalid_proxy):
        """从代理列表和文件中移除无效代理 - 兼容旧代码"""
        # 默认调用永久移除的代理失败处理方法
        self.handle_proxy_failure(invalid_proxy, permanent=True)

    def save_valid_proxies(self):
        """保存有效代理到文件"""
        try:
            if self.invalid_proxies:
                print(f"更新代理文件，移除 {len(self.invalid_proxies)} 个无效代理")
                valid_proxies = []
                
                with open('proxies.txt', 'r', encoding='utf-8') as file:
                    lines = file.readlines()
                    i = 0
                    proxy_lines = []
                    
                    while i < len(lines):
                        # 收集一个代理的所有行
                        if "协议:" in lines[i]:
                            proxy_start = i
                            for j in range(i, min(i+5, len(lines))):
                                if j < len(lines) and "密码:" in lines[j]:
                                    proxy_lines = lines[proxy_start:j+1]
                                    i = j + 1
                                    break
                            else:
                                i += 1
                                continue
                                
                            # 构建代理以进行比较
                            test_proxy = {}
                            username = ""
                            password = ""
                            
                            for line in proxy_lines:
                                if "协议:" in line:
                                    test_proxy['http'] = line.split(":")[1].strip() + "://"
                                elif "地址:" in line:
                                    test_proxy['http'] += line.split(":")[1].strip()
                                elif "端口:" in line:
                                    test_proxy['http'] += ":" + line.split(":")[1].strip()
                                elif "用户名:" in line:
                                    username = line.split(":")[1].strip()
                                elif "密码:" in line:
                                    password = line.split(":")[1].strip()
                            
                            if username and password:
                                test_proxy['http'] = test_proxy['http'].replace("://", f"://{username}:{password}@")
                            
                            # 检查是否为有效代理
                            if test_proxy not in self.invalid_proxies:
                                valid_proxies.extend(proxy_lines)
                        else:
                            i += 1
                
                # 写入有效代理
                with open('proxies.txt', 'w', encoding='utf-8') as file:
                    file.writelines(valid_proxies)
                    
                print(f"代理文件已更新，保留 {len(valid_proxies)//5} 个有效代理")
        except Exception as e:
            print(f"保存有效代理时出错: {e}")
    
    def handle_proxy_failure(self, proxy, permanent=False):
        """处理代理失败 - 对于123Proxy跳过失败处理
        permanent=True表示永久移除（如第一次连接就失败）
        permanent=False表示临时失败，允许重试
        """
        if not proxy:
            return

        # 对于123Proxy隧道代理，不处理代理失败
        # 因为每次请求都会自动分配不同的IP，某个IP失败不代表代理配置有问题
        if self.use_123proxy:
            print("123Proxy隧道代理每次请求都是不同IP，跳过失败处理")
            return

        with self.lock:
            proxy_str = str(proxy)  # 转为字符串以便用作字典键

            if permanent:
                # 永久移除（仅用于其他类型的代理）
                if proxy in self.proxies and proxy not in self.invalid_proxies:
                    self.invalid_proxies.append(proxy)
                    self.proxies.remove(proxy)
                    print(f"永久移除无效代理: {self.get_proxy_info(proxy)}")
                return
            
            # 临时失败，记录失败次数和时间
            if proxy_str not in self.proxy_failures:
                self.proxy_failures[proxy_str] = {'failures': 1, 'last_failure': time.time(), 'consecutive_failures': 1}
                print(f"代理首次失败: {self.get_proxy_info(proxy)}，记录失败次数 1/5")
            else:
                current_time = time.time()
                last_failure = self.proxy_failures[proxy_str]['last_failure']
                
                # 如果距离上次失败超过15分钟，重置连续失败次数
                if current_time - last_failure > 900:  # 15分钟
                    consecutive_failures = 1
                    print(f"代理失败: {self.get_proxy_info(proxy)}，超过15分钟，重置连续失败次数为 1/5")
                else:
                    consecutive_failures = self.proxy_failures[proxy_str]['consecutive_failures'] + 1
                    print(f"代理失败: {self.get_proxy_info(proxy)}，连续失败次数增加到 {consecutive_failures}/5")
                
                # 更新总失败次数
                failures = self.proxy_failures[proxy_str]['failures'] + 1
                
                # 更新代理失败信息
                self.proxy_failures[proxy_str] = {
                    'failures': failures, 
                    'last_failure': current_time,
                    'consecutive_failures': consecutive_failures
                }
                
                # 如果连续失败次数达到5次，暂时冷却该代理一段时间
                if consecutive_failures >= 5:
                    # 将代理标记为冷却状态，并记录冷却开始时间
                    self.proxy_failures[proxy_str]['cooling_until'] = current_time + 1800  # 冷却30分钟
                    print(f"代理已达到连续5次失败，冷却30分钟: {self.get_proxy_info(proxy)}")
                
                # 如果总失败次数达到15次，永久移除
                if failures >= 15:
                    if proxy in self.proxies and proxy not in self.invalid_proxies:
                        self.invalid_proxies.append(proxy)
                        self.proxies.remove(proxy)
                        print(f"代理总失败次数达到15次，永久移除: {self.get_proxy_info(proxy)}")
            
    def check_proxy(self, proxy):
        """检查代理是否可用 - 对于123Proxy跳过检查"""
        if not proxy:
            return False

        # 对于123Proxy隧道代理，跳过检查直接返回True
        # 因为每次请求都会自动分配不同的IP，检查没有意义
        if self.use_123proxy:
            return True

        # 检查代理是否处于冷却期（仅用于其他类型的代理）
        proxy_str = str(proxy)
        if proxy_str in self.proxy_failures and 'cooling_until' in self.proxy_failures[proxy_str]:
            cooling_until = self.proxy_failures[proxy_str]['cooling_until']
            if time.time() < cooling_until:
                print(f"代理 {self.get_proxy_info(proxy)} 仍在冷却期内，跳过使用")
                return False
            else:
                # 冷却期结束，重置连续失败计数
                self.proxy_failures[proxy_str]['consecutive_failures'] = 0
                del self.proxy_failures[proxy_str]['cooling_until']
                print(f"代理 {self.get_proxy_info(proxy)} 冷却期已结束，重新启用")
        
        # 测试网站列表 - 使用多个不同网站提高检测准确性
        test_urls = [
            'https://www.amazon.com/robots.txt',
            'https://www.google.com/robots.txt',
            'https://httpbin.org/ip'
        ]
        
        # 随机选择一个测试URL
        test_url = random.choice(test_urls)
        
        try:
            headers = self.Headers()
            start_time = time.time()
            response = requests.get(test_url, headers=headers, proxies=proxy, timeout=20)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                print(f"代理检查成功: {self.get_proxy_info(proxy)}, 响应时间: {response_time:.2f}秒")
                # 如果代理工作正常，重置其失败记录
                if proxy_str in self.proxy_failures:
                    self.proxy_failures[proxy_str]['consecutive_failures'] = 0
                return True
            else:
                print(f"代理返回非200状态: {self.get_proxy_info(proxy)}, 状态码: {response.status_code}")
                return False
                
        except requests.exceptions.Timeout:
            print(f"代理检查超时: {self.get_proxy_info(proxy)}")
            return False
        except requests.exceptions.ProxyError:
            print(f"代理连接错误: {self.get_proxy_info(proxy)}")
            # 对于123Proxy，不永久移除代理，只记录临时失败
            if self.use_123proxy:
                print("123Proxy连接错误，不移除代理，继续使用")
                return False  # 返回False但不移除代理
            else:
                # 其他代理类型标记为永久性失败
                self.handle_proxy_failure(proxy, permanent=True)
            return False
        except Exception as e:
            print(f"代理检查异常: {self.get_proxy_info(proxy)}, 错误: {str(e)}")
            return False

    def load_asins_from_excel(self, filename="asin.xlsx"):
        """从Excel文件加载ASINs"""
        try:
            df = pd.read_excel(filename)

            # 查找ASIN列，不区分大小写
            asin_col = None
            for col in df.columns:
                if col.upper() == 'ASIN':
                    asin_col = col
                    break

            if asin_col:
                asins = df[asin_col].tolist()
                print(f"从 {filename} 加载了 {len(asins)} 个ASIN (使用列: {asin_col})")
                return asins
            else:
                print(f"错误: {filename} 没有ASIN列")
                print(f"没有加载到ASIN，请检查Excel文件")
                print(f"可用的列名: {list(df.columns)}")
                return []
        except Exception as e:
            print(f"加载ASIN文件时出错: {e}")
            return []

    def load_failed_asins_for_retry(self, filename="Failed_To_Get_Info_ASINs.xlsx"):
        """从失败列表加载ASIN进行重试"""
        try:
            if not os.path.exists(filename):
                print(f"文件不存在: {filename}")
                return []

            df = pd.read_excel(filename)

            # 查找ASIN列，不区分大小写
            asin_col = None
            for col in df.columns:
                if col.upper() == 'ASIN':
                    asin_col = col
                    break

            if asin_col:
                asins = df[asin_col].dropna().tolist()
                print(f"从失败列表 {filename} 加载了 {len(asins)} 个ASIN进行重试 (使用列: {asin_col})")

                # 显示品牌信息（如果有的话）
                if '品牌' in df.columns:
                    brand_info = df[[asin_col, '品牌']].dropna()
                    print("包含的品牌信息:")
                    for _, row in brand_info.head(10).iterrows():  # 只显示前10个
                        print(f"  {row[asin_col]}: {row['品牌']}")
                    if len(brand_info) > 10:
                        print(f"  ... 还有 {len(brand_info) - 10} 个")

                return asins
            else:
                print(f"错误: {filename} 没有ASIN列")
                print(f"没有加载到ASIN，请检查Excel文件")
                print(f"可用的列名: {list(df.columns)}")
                return []
        except Exception as e:
            print(f"加载失败ASIN文件时出错: {e}")
            return []
    
    def save_results_to_excel(self, output_file="Amazon产品信息.xlsx"):
        """将结果保存到Excel文件"""
        with self.lock:
            if not self.valid_results:
                print("没有可以保存的有效结果")
                return
                
            try:
                # 准备数据
                data = {
                    'ASIN': [],
                    'URL': [],
                    '标题': [],
                    '品牌': [],
                    '品牌类型': [],
                    '尺寸': [],
                    '重量': [],
                    '评分': [],
                    '评论数': [],
                    '主类目排名': [],
                    '主类目名称': [],
                    '子类目排名': [],
                    '子类目名称': [],
                    '主图URL': []
                }
                
                # 获取最后一个结果进行保存，实现实时保存
                last_result = self.valid_results[-1]
                
                # 检查品牌是否唯一
                if not last_result['is_unique_brand']:
                    print(f"ASIN {last_result['asin']}: 品牌 {last_result['brand']} 不是唯一的，跳过保存")
                    return
                
                # 处理类目信息
                main_category_rank = None
                main_category_name = ""
                sub_category_rank = None
                sub_category_name = ""
                
                if last_result['sales_rank'] and len(last_result['sales_rank']) > 0:
                    main_category_rank = last_result['sales_rank'][0]['rank']
                    main_category_name = last_result['sales_rank'][0]['category']
                    
                if last_result['sales_rank'] and len(last_result['sales_rank']) > 1:
                    sub_category_rank = last_result['sales_rank'][1]['rank']
                    sub_category_name = last_result['sales_rank'][1]['category']
                
                # 添加数据
                data['ASIN'].append(last_result['asin'])
                data['URL'].append(last_result['url'])
                data['标题'].append(last_result['title'])
                data['品牌'].append(last_result['brand'])
                data['品牌类型'].append(last_result['brand_type'])
                data['尺寸'].append(last_result['dimensions'])
                data['重量'].append(last_result['weight'])
                data['评分'].append(last_result['rating'])
                data['评论数'].append(last_result['rating_count'])
                data['主类目排名'].append(main_category_rank)
                data['主类目名称'].append(main_category_name)
                data['子类目排名'].append(sub_category_rank)
                data['子类目名称'].append(sub_category_name)
                data['主图URL'].append(last_result['image_url'])
                
                # 创建DataFrame
                df = pd.DataFrame(data)
                
                # 根据品牌类型选择不同的输出文件
                if last_result['brand_type'] == "Visit the":
                    output_file = "Visit_the_品牌产品.xlsx"
                else:
                    output_file = "Brand_品牌产品.xlsx"
                
                # 检查文件是否存在
                if os.path.exists(output_file):
                    try:
                        # 检查文件是否可以访问(避免文件被锁定的情况)
                        temp_name = f"temp_{int(time.time())}.xlsx"
                        
                        # 读取现有数据并追加
                        try:
                            existing_df = pd.read_excel(output_file)
                            
                            # 检查是否ASIN已存在，避免重复添加
                            if last_result['asin'] in existing_df['ASIN'].values:
                                print(f"ASIN {last_result['asin']} 已存在于Excel中，跳过保存")
                                return
                                
                            combined_df = pd.concat([existing_df, df], ignore_index=True)
                            
                            # 先写入临时文件，然后重命名，避免长时间锁定主文件
                            combined_df.to_excel(temp_name, index=False)
                            
                            # 等待现有文件可访问，最多等待5秒
                            retry_count = 0
                            while retry_count < 5:
                                try:
                                    # 尝试删除旧文件并重命名新文件
                                    if os.path.exists(output_file):
                                        os.remove(output_file)
                                    os.rename(temp_name, output_file)
                                    break
                                except PermissionError:
                                    print(f"文件 {output_file} 正在被占用，等待1秒后重试...")
                                    time.sleep(1)
                                    retry_count += 1
                            
                            if retry_count >= 5:
                                print(f"无法访问文件 {output_file}，已将数据保存到 {temp_name}")
                            else:
                                print(f"已将 ASIN {last_result['asin']} 添加到 {output_file}")
                                
                        except Exception as e:
                            # 读取Excel失败，直接写入新文件
                            print(f"读取现有Excel出错: {str(e)}")
                            df.to_excel(output_file, index=False)
                            print(f"保存了 ASIN {last_result['asin']} 到新文件 {output_file}")
                    except Exception as e:
                        print(f"文件操作失败: {str(e)}")
                        # 尝试直接写入
                        df.to_excel(f"backup_{int(time.time())}_{output_file}", index=False)
                        print(f"已保存备份文件")
                else:
                    # 创建新文件
                    df.to_excel(output_file, index=False)
                    print(f"保存了 ASIN {last_result['asin']} 到新文件 {output_file}")
                    
            except Exception as e:
                print(f"保存结果到Excel时出错: {e}")
                print(f"错误详情: {str(e.__class__.__name__)}: {str(e)}")
                # 尝试保存到备份文件
                try:
                    backup_file = f"error_backup_{int(time.time())}.xlsx"
                    pd.DataFrame(data).to_excel(backup_file, index=False)
                    print(f"已保存数据到备份文件: {backup_file}")
                except:
                    print("备份文件也无法保存，放弃保存操作")
                import traceback
                traceback.print_exc()

    def get_brand(self, asin):
        """
        获取特定ASIN的品牌信息
        注意：此函数仅获取单个产品的品牌信息，不搜索相关产品
        
        :param asin: Amazon产品的ASIN码
        :return: (品牌类型, 品牌名称) 的元组，如果未找到则返回 (None, None)
        """
        amazon_url = f"https://www.amazon.com/dp/{asin}?language=en_US&zipcode=10001&LanguagePreference=en_US&country=US&ie=UTF8&ref_=nav_ya_signin&lc=en_US"
        retries = self.max_retries  # 使用配置的重试次数
        timeout_count = 0  # 记录超时次数

        # 对于123Proxy，跳过随机延迟，因为每次请求都是不同IP
        if not self.use_123proxy:
            delay = random.uniform(1.0, 2.0)
            time.sleep(delay)

        # 尝试3次使用随机代理
        for attempt in range(retries):
            headers = self.Headers()
            proxy = random.choice(self.proxies) if self.proxies else None
            proxy_info = self.get_proxy_info(proxy)

            try:
                # 设置cookies强制使用英文
                self.session.cookies.set('i18n-prefs', 'USD', domain='.amazon.com', path='/')
                self.session.cookies.set('lc-main', 'en_US', domain='.amazon.com', path='/')
                
                response = self.session.get(amazon_url, headers=headers, proxies=proxy, timeout=20)
                
                # 检查是否是验证码页面
                if response.status_code == 200:
                    if "Click the button below to continue shopping" in response.text:
                        if self.use_123proxy:
                            # 123Proxy会自动分配新IP，静默重试
                            pass
                        else:
                            print(f"ASIN {asin} 遇到点击继续购物验证码，将代理放入冷静期")
                            # 暂停当前代理（仅用于其他类型的代理）
                            if proxy:
                                self.suspend_proxy(proxy)
                                self.handle_proxy_failure(proxy, permanent=False)
                                print(f"已将代理放入冷静期，尝试使用新代理")
                        continue
                    elif "Type the characters you see in this image" in response.text or "captcha" in response.text.lower():
                        print(f"ASIN {asin} 遇到验证码，尝试解决")
                        
                        # 尝试解决验证码
                        captcha_response = self.solve_captcha(response)
                        if captcha_response:
                            # 使用验证后的响应替换原响应
                            response = captcha_response
                        else:
                            print(f"ASIN {asin} 验证码解决失败，尝试新代理")
                            time.sleep(random.uniform(2, 5))
                            continue
                    
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # 检查是否获取到英文页面
                    title = soup.find('title')
                    if title:
                        # 使用更准确的方法检测中文字符
                        def contains_chinese(text):
                            chinese_range = re.compile(u'[\u4e00-\u9fff]')
                            return bool(chinese_range.search(text))
                        
                        if contains_chinese(title.text):
                            print(f"ASIN {asin}: 获取到中文页面，尝试强制转换为英文")
                            # 先尝试使用新函数强制转换英文页面
                            english_response = self.force_english_page(response)
                            if english_response:
                                response = english_response
                                soup = BeautifulSoup(response.content, 'html.parser')
                                print(f"ASIN {asin}: 成功转换为英文页面")
                            else:
                                print(f"ASIN {asin}: 强制转换失败，尝试更新Cookie或使用新代理")
                                # 尝试更新Cookie
                                if attempt == 0 and self.update_cookies_with_selenium():
                                    print(f"ASIN {asin}: 更新Cookie成功，重试")
                                    continue
                                else:
                                    # 尝试新代理
                                    if proxy:
                                        self.handle_proxy_failure(proxy, permanent=False)
                                    continue
                    
                    # 首先检查是否有品牌元素
                    brand_element = soup.find('a', id='bylineInfo')
                    product_table_brand = soup.find('tr', class_='po-brand')
                    
                    if not brand_element and not product_table_brand:
                        print(f"ASIN {asin}: 未找到品牌元素，跳过品牌请求")
                        return None, None
                    
                    # 有品牌元素，继续处理
                    if brand_element:
                        brand_text = brand_element.text.strip()
                        if "Visit the" in brand_text:
                            brand = brand_text.replace("Visit the", "").replace("Store", "").strip()
                            brand_type = "Visit the"
                        else:
                            brand = brand_text.replace("Brand:", "").strip()
                            brand_type = "Brand:"
                            
                        print(f"ASIN {asin}: 品牌 {brand} (类型: {brand_type})")
                        return brand_type, brand
                    
                    # 尝试从产品表格中获取品牌信息
                    if product_table_brand:
                        brand_name_element = product_table_brand.find('td', class_='a-span9')
                        if brand_name_element and brand_name_element.find('span'):
                            brand = brand_name_element.find('span').text.strip()
                            brand_type = "Brand:"  # 从产品表格获取的默认为Brand类型
                            print(f"ASIN {asin}: 成功获取品牌 {brand} (类型: {brand_type})")
                            return brand_type, brand
                            
                    print(f"ASIN {asin}: 未找到品牌信息")
                elif response.status_code == 503:
                    if self.use_123proxy:
                        print(f"ASIN {asin}: 服务暂时不可用(503)，123Proxy会自动分配新IP，直接重试")
                        continue
                    else:
                        backoff_time = self.calculate_backoff_time(attempt)
                        print(f"ASIN {asin}: 服务暂时不可用(503)，等待 {backoff_time:.2f} 秒后重试")
                        time.sleep(backoff_time)
                        continue
                elif response.status_code == 429:  # Too Many Requests
                    backoff_time = self.calculate_backoff_time(attempt, base_delay=15)
                    print(f"ASIN {asin}: 请求过多(429)，等待 {backoff_time:.2f} 秒后使用新代理重试")
                    if proxy:
                        self.handle_proxy_failure(proxy, permanent=False)
                    time.sleep(backoff_time)
                    continue
                elif response.status_code == 500:
                    print(f"ASIN {asin}: 服务器错误(500)，直接重试")
                    continue
                else:
                    print(f"ASIN {asin}: 请求失败，状态码 {response.status_code}")
                    if proxy:
                        self.handle_proxy_failure(proxy, permanent=False)
                    backoff_time = self.calculate_backoff_time(attempt, base_delay=3)
                    time.sleep(backoff_time)
                    continue

            except requests.exceptions.ProxyError:
                print(f"ASIN {asin}: 代理连接错误")
                if proxy:
                    self.handle_proxy_failure(proxy, permanent=False)
                continue  # 直接尝试新代理
            except requests.exceptions.Timeout:
                print(f"ASIN {asin}: 请求超时，可能是代理问题")
                if proxy:
                    self.handle_proxy_failure(proxy, permanent=False)
                timeout_count += 1
                continue  # 直接尝试新代理
            except requests.exceptions.ConnectionError:
                print(f"ASIN {asin}: 连接错误，可能是代理问题")
                if proxy:
                    self.handle_proxy_failure(proxy, permanent=False)
                continue  # 直接尝试新代理
            except Exception as e:
                print(f"ASIN {asin}: 未知错误 {str(e)}")
                backoff_time = self.calculate_backoff_time(attempt, base_delay=2)
                time.sleep(backoff_time)
                continue

        # 如果重试了3次仍然超时，记录主ASIN到超时表格
        if timeout_count >= retries:
            print(f"ASIN {asin}: 请求超时达到重试上限 {retries} 次，将保存主ASIN到超时表格")
            # 保存主ASIN到超时表格
            if hasattr(self, 'current_main_asin'):
                self.save_timeout_asin(self.current_main_asin, None)
        else:
            print(f"ASIN {asin}: 无法获取品牌信息，已达到重试次数上限")
        
        return None, None

    def calculate_backoff_time(self, attempt, base_delay=5, max_delay=300):
        """
        计算指数退避等待时间
        :param attempt: 当前尝试次数（从0开始）
        :param base_delay: 基础延迟时间（秒）
        :param max_delay: 最大延迟时间（秒）
        :return: 计算后的延迟时间（秒）
        """
        # 计算指数退避时间：基础延迟 * 2^尝试次数 + 随机抖动
        delay = min(base_delay * (2 ** attempt), max_delay)
        # 添加0-100%的随机抖动
        jitter = random.uniform(0, delay)
        return delay + jitter

    def search_brand_on_amazon(self, brand):
        """
        在亚马逊上搜索品牌并返回相关产品的ASIN列表
        注意：此函数用于搜索特定品牌的相关产品，与get_brand函数不同
        
        :param brand: 要搜索的品牌名称
        :return: 找到的相关产品ASIN列表，最多返回20个
        """
        search_url = f"https://www.amazon.com/s?k={brand.replace(' ', '+')}&language=en_US&country=US&ie=UTF8&ref_=nav_ya_signin&lc=en_US&zipcode=10001"
        retries = self.max_retries  # 使用配置的重试次数
        
        print(f"搜索品牌 '{brand}'")
        
        for attempt in range(retries):
            # 每次尝试都重新生成随机头部和选择新的随机代理
            headers = self.Headers()
            # 使用新函数获取可用代理
            proxy = self.get_available_proxy()
            proxy_info = self.get_proxy_info(proxy) if proxy else "无代理"
            
            # 对于123Proxy，跳过随机延迟，因为每次请求都是不同IP
            if not self.use_123proxy:
                delay = random.uniform(1.0, 2.0)
                print(f"搜索品牌 '{brand}' 添加随机延迟: {delay:.2f}秒")
                time.sleep(delay)
            
            try:
                # 设置cookies强制使用英文
                self.session.cookies.set('i18n-prefs', 'USD', domain='.amazon.com', path='/')
                self.session.cookies.set('lc-main', 'en_US', domain='.amazon.com', path='/')
                
                response = self.session.get(search_url, headers=headers, proxies=proxy, timeout=20)
                
                if response.status_code == 200:
                    # 检查是否是验证码页面
                    if "Click the button below to continue shopping" in response.text:
                        if self.use_123proxy:
                            # 123Proxy会自动分配新IP，静默重试
                            pass
                        else:
                            print(f"搜索品牌 '{brand}' 遇到点击继续购物验证码，将代理放入冷静期")
                            # 暂停当前代理（仅用于其他类型的代理）
                            if proxy:
                                self.suspend_proxy(proxy)
                                self.handle_proxy_failure(proxy, permanent=False)
                            # 直接跳过当前代理，不尝试解决验证码或更新Cookie
                            print(f"已将代理放入冷静期，尝试使用新代理")
                        continue
                    
                    # 检查是否是图片验证码
                    elif "Type the characters you see in this image" in response.text or ("captcha" in response.text.lower() and "Click the button below" not in response.text):
                        print(f"搜索品牌 '{brand}' 遇到图片验证码，尝试解决")
                        # 注意：不暂停代理，只有点击购物验证才暂停
                        
                        # 尝试解决验证码
                        captcha_response = self.solve_captcha(response)
                        if captcha_response:
                            # 使用验证后的响应替换原响应
                            response = captcha_response
                        else:
                            print(f"搜索品牌 '{brand}' 验证码解决失败，尝试新代理")
                            continue  # 直接进入下一次循环，会选择新的代理
                    
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # 检查是否获取到英文页面
                    title = soup.find('title')
                    if title and title.text:
                        # 使用更准确的方法检测中文字符
                        def contains_chinese(text):
                            chinese_range = re.compile(u'[\u4e00-\u9fff]')
                            return bool(chinese_range.search(text))
                        
                        if contains_chinese(title.text):
                            print(f"搜索品牌 '{brand}': 获取到中文页面，尝试强制转换为英文")
                            # 先尝试使用新函数强制转换英文页面
                            english_response = self.force_english_page(response)
                            if english_response:
                                response = english_response
                                soup = BeautifulSoup(response.content, 'html.parser')
                                print(f"搜索品牌 '{brand}': 成功转换为英文页面")
                            else:
                                print(f"搜索品牌 '{brand}': 强制转换失败，尝试更新Cookie或使用新代理")
                                # 尝试更新Cookie
                                if attempt == 0 and self.update_cookies_with_selenium():
                                    print(f"搜索品牌 '{brand}': 更新Cookie成功，重试")
                                    continue
                                else:
                                    # 尝试新代理
                                    if proxy:
                                        self.handle_proxy_failure(proxy, permanent=False)
                                    continue
                    
                    # 查找搜索结果中的产品
                    results = soup.find_all('div', attrs={'data-asin': True})
                    asins = []
                    
                    for result in results:
                        asin = result.get('data-asin')
                        if asin and asin.strip() and asin.startswith('B0'):  # 确保是有效的ASIN
                            asins.append(asin)
                            # 只获取前20个ASIN
                            if len(asins) >= 20:
                                break
                    
                    total_found = len(asins)
                    print(f"搜索品牌 '{brand}': 找到 {total_found} 个ASIN")
                    return asins
                elif response.status_code == 503:
                    if self.use_123proxy:
                        print(f"搜索品牌 '{brand}': 服务暂时不可用(503)，123Proxy会自动分配新IP，直接重试")
                        continue
                    else:
                        backoff_time = self.calculate_backoff_time(attempt)
                        print(f"搜索品牌 '{brand}': 服务暂时不可用(503)，等待 {backoff_time:.2f} 秒后重试")
                        time.sleep(backoff_time)
                        continue
                elif response.status_code == 500:
                    print(f"搜索品牌 '{brand}': 服务器错误(500)，直接重试")
                    continue
                elif response.status_code == 429:  # Too Many Requests
                    backoff_time = self.calculate_backoff_time(attempt, base_delay=20)
                    print(f"搜索品牌 '{brand}': 请求过多(429)，等待 {backoff_time:.2f} 秒后尝试新代理")
                    if proxy:
                        self.handle_proxy_failure(proxy, permanent=False)
                    time.sleep(backoff_time)
                    continue
                else:
                    print(f"搜索品牌 '{brand}' 失败，状态码: {response.status_code}，尝试新代理")
                    # if proxy:
                    #     # 考虑将此代理添加到无效列表
                    #      self.handle_proxy_failure(proxy, permanent=False)
                    backoff_time = self.calculate_backoff_time(attempt, base_delay=3)
                    time.sleep(backoff_time)
                    continue  # 使用新代理重试
                    
            except requests.exceptions.ProxyError:
                print(f"搜索品牌 '{brand}': 代理连接错误，移除此代理")
                if proxy:
                    self.handle_proxy_failure(proxy, permanent=False)
                continue  # 直接尝试新代理
            except requests.exceptions.Timeout:
                print(f"搜索品牌 '{brand}': 请求超时，可能是代理问题，尝试新代理")
                if proxy:
                    self.handle_proxy_failure(proxy, permanent=False)
                continue  # 直接尝试新代理
            except requests.exceptions.ConnectionError:
                print(f"搜索品牌 '{brand}': 连接错误，可能是代理问题，尝试新代理")
                if proxy:
                    self.handle_proxy_failure(proxy, permanent=False)
                continue  # 直接尝试新代理
            except Exception as e:
                print(f"搜索品牌 '{brand}' 时出错: {str(e)}")
                backoff_time = self.calculate_backoff_time(attempt, base_delay=2)
                time.sleep(backoff_time)
                continue  # 使用新代理重试
        
        print(f"搜索品牌 '{brand}': 已达重试次数上限，无法获取结果")
        return []

    def process_asin(self, asin):
        """处理单个ASIN的函数"""
        try:
            # 检查ASIN是否已处理
            with self.lock:
                if asin in self.processed_asins:
                    return
                self.processed_asins.add(asin)
                # 记录当前正在处理的主ASIN，用于超时记录
                self.current_main_asin = asin

            print(f"\n开始抓取 ASIN: {asin}")
            
            # 获取产品信息
            product_info = self.get_amazon_product_info(asin)
            product_info['main_asin'] = asin  # 确保记录主ASIN
            
            # 检查条件：产品必须断货
            if not product_info['is_out_of_stock']:
                self.add_non_compliant_asin(asin, '产品有货', asin)
                with self.lock:
                    self.processed_count += 1
                    if self.progress_bar:
                        self.progress_bar.update(1)
                return
                
            print(f"ASIN {asin}: 产品断货，符合条件")
            
            # 检查条件：不要变体产品
            if product_info['has_variants']:
                self.add_non_compliant_asin(asin, '有多个变体', asin)
                with self.lock:
                    self.processed_count += 1
                    if self.progress_bar:
                        self.progress_bar.update(1)
                return
                
            # 检查条件：评论数在20-2000之间
            if not product_info['rating_count'] or product_info['rating_count'] < 20 or product_info['rating_count'] > 2000:
                reason = f'评论数 {product_info["rating_count"]} 不在要求范围内(20-2000)'
                self.add_non_compliant_asin(asin, reason, asin)
                with self.lock:
                    self.processed_count += 1
                    if self.progress_bar:
                        self.progress_bar.update(1)
                return
                
            print(f"ASIN {asin}: 评论数 {product_info['rating_count']} 符合要求(20-2000)")
            
            # 获取品牌信息前，先判断是否有品牌元素
            soup = BeautifulSoup(product_info['html_content'] if 'html_content' in product_info else "", 'html.parser')
            brand_element = soup.find('a', id='bylineInfo')
            product_table_brand = soup.find('tr', class_='po-brand')
            
            if not brand_element and not product_table_brand:
                print(f"ASIN {asin}: 未找到品牌元素，跳过品牌请求")
            else:
                # 有品牌元素，检查品牌是否唯一
                try:
                    # 检查品牌是否唯一
                    if not product_info['is_unique_brand']:
                        print(f"ASIN {asin}: 品牌 {product_info['brand']} 不是唯一的，跳过")
                        with self.lock:
                            self.non_unique_brand_asins.append({
                                'asin': asin,
                                'brand': product_info['brand'] if 'brand' in product_info else 'Unknown',
                                'main_asin': asin
                            })
                            self.check_batch_save_non_unique_brand()
                            self.processed_count += 1
                            if self.progress_bar:
                                self.progress_bar.update(1)
                        return
                        
                    print(f"ASIN {asin}: 品牌 {product_info['brand']} 是唯一的")
                except Exception as e:
                    # 处理超时情况
                    print(f"ASIN {asin}: 检查品牌是否唯一时超时: {str(e)}")
                    # 确保保存的是主ASIN
                    with self.lock:
                        self.timeout_asins.append({
                            'asin': asin,
                            'brand': product_info['brand'] if 'brand' in product_info else 'Unknown',
                            'main_asin': asin
                        })
                        self.check_batch_save_timeout()
                        self.processed_count += 1
                        if self.progress_bar:
                            self.progress_bar.update(1)
                    return
            
            # 所有条件满足，添加到有效结果
            with self.lock:
                # 根据品牌类型分类
                if 'brand_type' in product_info and product_info['brand_type'] == "Visit the":
                    self.visit_the_brand_asins.append(product_info)
                    self.check_batch_save_visit_the_brand()
                else:
                    self.other_brand_asins.append(product_info)
                    self.check_batch_save_other_brand()
                
                self.success_count += 1
            
            print(f"ASIN {asin} 处理完成")
            
        except Exception as e:
            print(f"处理 ASIN {asin} 时发生错误: {str(e)}")
            # 保存到超时/错误列表
            with self.lock:
                self.timeout_asins.append({
                    'asin': asin,
                    'brand': 'Unknown',
                    'error': str(e),
                    'main_asin': asin
                })
                self.check_batch_save_timeout()
                self.error_count += 1
        finally:
            # 更新进度
            with self.lock:
                self.processed_count += 1
                if self.progress_bar:
                    self.progress_bar.update(1)
                    
    def save_timeout_asin(self, asin, brand=None):
        """保存超时的ASIN到超时表格 - 兼容旧代码，内部调用新的批处理方法"""
        with self.lock:
            self.timeout_asins.append({
                'asin': asin,
                'brand': brand if brand else 'Unknown',
                'main_asin': asin
            })
            self.check_batch_save_timeout()

    def process_asins_with_threads(self, num_threads=5):
        """使用ThreadPoolExecutor处理ASIN列表"""
        self.asins = self.load_asins_from_excel()
        if not self.asins:
            print("没有加载到ASIN，请检查Excel文件")
            return

        print(f"加载了 {len(self.asins)} 个ASIN")
        self.results = []
        self.lock = threading.Lock()
        
        # 初始化或重置缓存列表
        self.valid_results = []
        self.timeout_asins = []
        self.non_unique_brand_asins = []
        self.non_compliant_asins = []
        self.failed_to_get_info_asins = []  # 新增：无法获取产品信息的ASIN
        self.visit_the_brand_asins = []
        self.other_brand_asins = []
        self.processed_asins = set()
        
        # 如果存在检查点，从检查点继续
        start_index = 0
        if self.current_position and self.current_position.startswith("process_asins:"):
            try:
                start_index = int(self.current_position.split(":")[1])
                print(f"从检查点继续处理，跳过前 {start_index} 个ASIN")
                self.asins = self.asins[start_index:]
            except:
                print("检查点格式错误，从头开始处理")
        
        # 创建进度条
        with tqdm(total=len(self.asins), desc="处理ASIN") as pbar:
            self.progress_bar = pbar
            self.stop_event = threading.Event()
            
            # 使用ThreadPoolExecutor替代手动创建线程
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                # 提交所有任务
                futures = {executor.submit(self.process_asin, asin): asin for asin in self.asins}
                
                processed = 0
                # 处理完成的任务
                for future in concurrent.futures.as_completed(futures):
                    asin = futures[future]
                    try:
                        # 获取结果(如果process_asin有返回值)
                        result = future.result()
                    except Exception as e:
                        print(f"处理ASIN {asin}时出错: {str(e)}")
                    
                    # 更新进度和检查点
                    processed += 1
                    current_pos = start_index + processed
                    self.update_checkpoint(f"process_asins:{current_pos}")
                    
                    # 更新进度条(由于process_asin内部已经更新进度条，这里不需要重复更新)
                    if self.stop_event.is_set():
                        break
        
        # 保存剩余的结果
        self.save_remaining_results()
        
        print(f"处理完成，共处理 {self.processed_count} 个ASIN，成功 {self.success_count} 个，错误 {self.error_count} 个")
        
        # 任务完成，删除检查点
        self.delete_checkpoint()

    def filter_patents(self, asin):
        """
        检查产品是否有专利
        :param asin: Amazon产品的ASIN码
        :return: 包含专利信息的字典
        """
        result = {
            "asin": asin,
            "has_patent": False,
            "patent_info": None
        }
        
        url = f"https://www.amazon.com/dp/{asin}?language=en_US&zipcode=10001"
        headers = self.Headers()
        proxy = random.choice(self.proxies) if self.proxies else None
        
        try:
            response = self.session.get(url, headers=headers, proxies=proxy, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 在产品描述、特性和详情中查找专利相关词汇
                product_description = soup.find('div', id='productDescription')
                features = soup.find('div', id='feature-bullets')
                details = soup.find('div', id='detailBullets_feature_div')
                
                patent_keywords = ['patent', 'patented', 'patent pending', 'patent-pending', 'US patent']
                
                # 检查产品描述
                if product_description:
                    description_text = product_description.get_text().lower()
                    for keyword in patent_keywords:
                        if keyword in description_text:
                            result["has_patent"] = True
                            result["patent_info"] = f"Found keyword '{keyword}' in product description"
                            return result
                
                # 检查产品特性
                if features:
                    features_text = features.get_text().lower()
                    for keyword in patent_keywords:
                        if keyword in features_text:
                            result["has_patent"] = True
                            result["patent_info"] = f"Found keyword '{keyword}' in product features"
                            return result
                
                # 检查产品详情
                if details:
                    details_text = details.get_text().lower()
                    for keyword in patent_keywords:
                        if keyword in details_text:
                            result["has_patent"] = True
                            result["patent_info"] = f"Found keyword '{keyword}' in product details"
                            return result
                
                print(f"ASIN {asin}: 未找到专利信息")
            else:
                print(f"ASIN {asin}: 请求失败，状态码 {response.status_code}")
                
        except Exception as e:
            print(f"检查专利信息时出错: {str(e)}")
            
        return result

    def save_patent_results_to_excel(self, output_file="Patent_Results.xlsx"):
        """将专利筛选结果保存到Excel文件"""
        with self.lock:
            if not self.patent_results:
                print("没有可以保存的专利筛选结果")
                return
                
            try:
                # 准备数据
                data = {
                    'ASIN': [],
                    '是否有专利': [],
                    '专利信息': []
                }
                
                for result in self.patent_results:
                    data['ASIN'].append(result['asin'])
                    data['是否有专利'].append('是' if result['has_patent'] else '否')
                    data['专利信息'].append(result['patent_info'] if result['patent_info'] else 'N/A')
                
                # 创建DataFrame
                df = pd.DataFrame(data)
                
                # 保存到Excel
                df.to_excel(output_file, index=False)
                print(f"专利筛选结果已保存到 {output_file}")
                    
            except Exception as e:
                print(f"保存专利结果到Excel时出错: {e}")

    def process_patent_filtering(self, num_threads=5):
        """使用ThreadPoolExecutor处理专利筛选"""
        self.patent_results = []
        self.patent_lock = threading.Lock()
        self.patent_stop_event = threading.Event()
        
        # 加载Excel中的ASIN
        try:
            df = pd.read_excel("Amazon产品信息.xlsx")
            asins = df['ASIN'].tolist()
        except Exception as e:
            print(f"加载Excel文件出错: {str(e)}")
            return

        print(f"加载了 {len(asins)} 个ASIN进行专利筛选")
        
        # 如果存在检查点，从检查点继续
        start_index = 0
        if self.current_position and self.current_position.startswith("process_patents:"):
            try:
                start_index = int(self.current_position.split(":")[1])
                print(f"从检查点继续处理专利，跳过前 {start_index} 个ASIN")
                asins = asins[start_index:]
            except:
                print("检查点格式错误，从头开始处理专利")

        # 使用进度条显示进度
        with tqdm(total=len(asins), desc="专利筛选") as pbar:
            processed = 0
            
            # 使用ThreadPoolExecutor处理任务
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                # 提交所有任务
                futures = {executor.submit(self.filter_patents, asin): asin for asin in asins}
                
                # 处理完成的任务
                for future in concurrent.futures.as_completed(futures):
                    asin = futures[future]
                    try:
                        result = future.result()
                        if result:
                            with self.patent_lock:
                                self.patent_results.append(result)
                    except Exception as e:
                        print(f"处理ASIN {asin}专利时出错: {str(e)}")
                    
                    # 更新进度和检查点
                    processed += 1
                    current_pos = start_index + processed
                    self.update_checkpoint(f"process_patents:{current_pos}")
                    pbar.update(1)
                    
                    if self.patent_stop_event.is_set():
                        break

        # 保存结果到Excel
        if self.patent_results:
            self.save_patent_results_to_excel()
            print(f"已保存 {len(self.patent_results)} 条专利筛选结果到Excel")
            # 任务完成，删除检查点
            self.delete_checkpoint()
        else:
            print("没有找到任何专利信息")

    def process_historical_data(self, num_threads=1):
        """使用ThreadPoolExecutor处理历史数据"""
        self.historical_results = []
        self.historical_lock = threading.Lock()
        self.historical_stop_event = threading.Event()
        
        # 加载Excel中的ASIN
        try:
            df = pd.read_excel("Patent_Results.xlsx")
            asins = df['ASIN'].tolist()
        except Exception as e:
            print(f"加载Excel文件出错: {str(e)}")
            return

        print(f"加载了 {len(asins)} 个ASIN进行历史数据分析")
        
        # 如果存在检查点，从检查点继续
        start_index = 0
        if self.current_position and self.current_position.startswith("process_historical:"):
            try:
                start_index = int(self.current_position.split(":")[1])
                print(f"从检查点继续处理历史数据，跳过前 {start_index} 个ASIN")
                asins = asins[start_index:]
            except:
                print("检查点格式错误，从头开始处理历史数据")

        # 使用进度条显示进度
        with tqdm(total=len(asins), desc="历史数据分析") as pbar:
            processed = 0
            
            # 使用ThreadPoolExecutor处理任务
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                # 提交所有任务
                futures = {executor.submit(self.get_keepa_info, asin): asin for asin in asins}
                
                # 处理完成的任务
                for future in concurrent.futures.as_completed(futures):
                    asin = futures[future]
                    try:
                        result = future.result()
                        if result:
                            with self.historical_lock:
                                self.historical_results.append(result)
                    except Exception as e:
                        print(f"处理ASIN {asin}历史数据时出错: {str(e)}")
                    
                    # 更新进度和检查点
                    processed += 1
                    current_pos = start_index + processed
                    self.update_checkpoint(f"process_historical:{current_pos}")
                    pbar.update(1)
                    
                    if self.historical_stop_event.is_set():
                        break

        # 保存结果到Excel
        if self.historical_results:
            self.save_historical_results_to_excel()
            print(f"已保存 {len(self.historical_results)} 条历史数据分析结果到Excel")
            # 任务完成，删除检查点
            self.delete_checkpoint()
        else:
            print("没有找到任何历史数据")

    def save_historical_results_to_excel(self, output_file="Historical_Data.xlsx"):
        """将历史价格和缺货时间保存到Excel文件"""
        with self.lock:
            if not hasattr(self, 'historical_results') or not self.historical_results:
                print("没有可以保存的历史数据结果")
                return
                
            try:
                # 准备数据
                data = {
                    'ASIN': [],
                    '历史最高价': [],
                    '一个月内是否断货': []
                }
                
                for result in self.historical_results:
                    data['ASIN'].append(result['asin'])
                    data['历史最高价'].append(result['historical_highest_price'] if result['historical_highest_price'] else 'N/A')
                    data['一个月内是否断货'].append('是' if result['out_of_stock_within_month'] == 1 else '否')
                
                # 创建DataFrame
                df = pd.DataFrame(data)
                
                # 保存到Excel
                df.to_excel(output_file, index=False)
                print(f"历史数据结果已保存到 {output_file}")
                    
            except Exception as e:
                print(f"保存历史数据到Excel时出错: {e}")

    def load_checkpoint(self):
        """加载检查点文件，获取上次执行到的位置"""
        try:
            if os.path.exists(self.checkpoint_file):
                with open(self.checkpoint_file, 'r') as f:
                    checkpoint_data = f.read().strip()
                    print(f"找到检查点文件，将从位置 {checkpoint_data} 继续执行")
                    return checkpoint_data
        except Exception as e:
            print(f"读取检查点文件时出错: {str(e)}")
        return None
        
    def update_checkpoint(self, position):
        """更新检查点文件"""
        try:
            with open(self.checkpoint_file, 'w') as f:
                f.write(str(position))
        except Exception as e:
            print(f"更新检查点文件时出错: {str(e)}")
            
    def delete_checkpoint(self):
        """删除检查点文件"""
        try:
            if os.path.exists(self.checkpoint_file):
                os.remove(self.checkpoint_file)
                print("执行完成，已删除检查点文件")
        except Exception as e:
            print(f"删除检查点文件时出错: {str(e)}")

    def switch_to_global_proxy(self):
        """对于123Proxy隧道代理，不需要切换全局代理"""
        if self.use_123proxy:
            print("使用123Proxy隧道代理，不需要切换全局代理")
            return None

        # 原有的全局代理切换逻辑（用于其他类型的代理）
        if not self.proxies:
            print("没有可用的代理")
            return None

        # 从可用代理列表中选择一个
        proxy = self.get_available_proxy()
        if not proxy:
            print("没有可用的代理")
            return None

        print(f"切换到全局代理: {proxy}")

        # 设置全局代理
        os.environ['HTTP_PROXY'] = proxy
        os.environ['HTTPS_PROXY'] = proxy
        
        # 更新session的代理
        self.session.proxies = {
            'http': proxy,
            'https': proxy
        }
        # 返回代理字符串，而不是字典
        return proxy

    def process_brand_search(self, brand_name, output_file="Brand_Search_Results.xlsx"):
        """
        搜索特定品牌并保存结果
        :param brand_name: 要搜索的品牌名称
        :param output_file: 输出文件名
        """
        print(f"开始搜索品牌: {brand_name}")
        
        # 搜索品牌获取相关ASIN
        asins = self.search_brand_on_amazon(brand_name)
        
        if not asins:
            print(f"未找到与品牌 '{brand_name}' 相关的产品")
            return
        
        print(f"搜索到 {len(asins)} 个与品牌 '{brand_name}' 相关的产品")
        
        # 创建结果列表
        search_results = []
        
        # 限制最多处理20个产品
        max_products = min(20, len(asins))
        
        # 使用进度条
        with tqdm(total=max_products, desc=f"处理品牌 '{brand_name}' 的产品") as pbar:
            for i in range(max_products):
                asin = asins[i]
                
                try:
                    # 获取品牌信息
                    brand_type, brand_name_found = self.get_brand(asin)
                    
                    # 保存结果
                    search_results.append({
                        "ASIN": asin,
                        "品牌": brand_name_found,
                        "品牌类型": brand_type,
                        "URL": f"https://www.amazon.com/dp/{asin}"
                    })
                    
                    # 随机延迟，避免被亚马逊检测
                    delay = random.uniform(1.0, 2.0)
                    time.sleep(delay)
                    
                except Exception as e:
                    print(f"处理ASIN {asin}时出错: {str(e)}")
                
                pbar.update(1)
        
        # 保存结果到Excel
        if search_results:
            df = pd.DataFrame(search_results)
            df.to_excel(output_file, index=False)
            print(f"已保存 {len(search_results)} 条品牌搜索结果到 {output_file}")
        else:
            print("没有找到可保存的品牌搜索结果")

    # 添加批量保存检查函数
    def check_batch_save_timeout(self):
        """检查并批量保存超时ASIN"""
        if len(self.timeout_asins) >= self.batch_size:
            self.save_timeout_asins_batch()
    
    def check_batch_save_non_unique_brand(self):
        """检查并批量保存非唯一品牌ASIN"""
        if len(self.non_unique_brand_asins) >= self.batch_size:
            self.save_non_unique_brand_asins_batch()
    
    def check_batch_save_non_compliant(self):
        """检查并批量保存不符合条件的ASIN"""
        if len(self.non_compliant_asins) >= self.batch_size:
            self.save_non_compliant_asins_batch()

    def check_batch_save_failed_to_get_info(self):
        """检查并批量保存无法获取信息的ASIN"""
        if len(self.failed_to_get_info_asins) >= self.batch_size:
            self.save_failed_to_get_info_asins_batch()
    
    def check_batch_save_visit_the_brand(self):
        """检查并批量保存Visit the品牌ASIN"""
        if len(self.visit_the_brand_asins) >= self.batch_size:
            self.save_visit_the_brand_asins_batch()
    
    def check_batch_save_other_brand(self):
        """检查并批量保存其他品牌ASIN"""
        if len(self.other_brand_asins) >= self.batch_size:
            self.save_other_brand_asins_batch()
    
    # 实现各类批量保存函数
    def save_timeout_asins_batch(self):
        """批量保存超时ASIN到Excel"""
        try:
            if not self.timeout_asins:
                return
                
            # 准备数据
            data = {
                'ASIN': [item['asin'] for item in self.timeout_asins],
                '品牌': [item['brand'] for item in self.timeout_asins],
                '主ASIN': [item['main_asin'] for item in self.timeout_asins]
            }
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 保存到Excel
            output_file = "Timeout_ASINs.xlsx"
            self.save_to_excel_with_duplication_check(df, output_file, 'ASIN')
            
            print(f"✓ 保存 {len(self.timeout_asins)} 个超时ASIN")

            # 清空缓存
            self.timeout_asins = []

        except Exception as e:
            print(f"保存超时ASIN时出错: {str(e)}")
    
    def save_non_unique_brand_asins_batch(self):
        """批量保存非唯一品牌ASIN到Excel"""
        try:
            if not self.non_unique_brand_asins:
                return
                
            # 准备数据
            data = {
                'ASIN': [item['asin'] for item in self.non_unique_brand_asins],
                '品牌': [item['brand'] for item in self.non_unique_brand_asins],
                '主ASIN': [item['main_asin'] for item in self.non_unique_brand_asins]
            }
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 保存到Excel
            output_file = "Non_Unique_Brand_ASINs.xlsx"
            self.save_to_excel_with_duplication_check(df, output_file, 'ASIN')
            
            print(f"✓ 保存 {len(self.non_unique_brand_asins)} 个非唯一品牌ASIN")

            # 清空缓存
            self.non_unique_brand_asins = []

        except Exception as e:
            print(f"保存非唯一品牌ASIN时出错: {str(e)}")
    
    def save_non_compliant_asins_batch(self):
        """批量保存不符合条件的ASIN到Excel"""
        try:
            if not self.non_compliant_asins:
                return

            # 验证数据格式并修复缺失的原因
            validated_data = []
            for item in self.non_compliant_asins:
                if isinstance(item, dict):
                    # 确保所有必需字段都存在
                    validated_item = {
                        'asin': item.get('asin', 'Unknown'),
                        'reason': item.get('reason', '未知原因'),
                        'main_asin': item.get('main_asin', item.get('asin', 'Unknown'))
                    }
                    validated_data.append(validated_item)
                else:
                    # 如果是字符串（旧格式），转换为字典
                    validated_item = {
                        'asin': str(item),
                        'reason': '格式错误 - 缺少原因信息',
                        'main_asin': str(item)
                    }
                    validated_data.append(validated_item)
                    print(f"警告: ASIN {item} 缺少原因信息，已添加默认原因")

            # 准备数据
            data = {
                'ASIN': [item['asin'] for item in validated_data],
                '原因': [item['reason'] for item in validated_data],
                '主ASIN': [item['main_asin'] for item in validated_data]
            }

            # 创建DataFrame
            df = pd.DataFrame(data)

            # 保存到Excel
            output_file = "Non_Compliant_ASINs.xlsx"
            self.save_to_excel_with_duplication_check(df, output_file, 'ASIN')

            print(f"✓ 保存 {len(validated_data)} 个不合规ASIN")

            # 清空缓存
            self.non_compliant_asins = []

        except Exception as e:
            print(f"保存不合规ASIN时出错: {str(e)}")

    def save_failed_to_get_info_asins_batch(self):
        """批量保存无法获取信息的ASIN到Excel"""
        try:
            if not self.failed_to_get_info_asins:
                return

            # 验证数据格式并修复缺失的字段
            validated_data = []
            for item in self.failed_to_get_info_asins:
                if isinstance(item, dict):
                    # 确保所有必需字段都存在
                    validated_item = {
                        'asin': item.get('asin', 'Unknown'),
                        'brand': item.get('brand', 'Unknown'),
                        'error_reason': item.get('error_reason', '无法获取产品信息'),
                        'main_asin': item.get('main_asin', item.get('asin', 'Unknown'))
                    }
                    validated_data.append(validated_item)
                else:
                    # 如果是字符串（旧格式），转换为字典
                    validated_item = {
                        'asin': str(item),
                        'brand': 'Unknown',
                        'error_reason': '格式错误 - 缺少详细信息',
                        'main_asin': str(item)
                    }
                    validated_data.append(validated_item)
                    print(f"警告: ASIN {item} 格式错误，已添加默认信息")

            # 准备数据
            data = {
                'ASIN': [item['asin'] for item in validated_data],
                '品牌': [item['brand'] for item in validated_data],
                '错误原因': [item['error_reason'] for item in validated_data],
                '主ASIN': [item['main_asin'] for item in validated_data]
            }

            # 创建DataFrame
            df = pd.DataFrame(data)

            # 保存到Excel
            output_file = "Failed_To_Get_Info_ASINs.xlsx"
            self.save_to_excel_with_duplication_check(df, output_file, 'ASIN')

            print(f"✓ 保存 {len(validated_data)} 个失败ASIN到 {output_file}")

            # 清空缓存
            self.failed_to_get_info_asins = []

        except Exception as e:
            print(f"保存失败ASIN时出错: {str(e)}")
    
    def save_visit_the_brand_asins_batch(self):
        """批量保存Visit the品牌ASIN到Excel"""
        try:
            if not self.visit_the_brand_asins:
                return
                
            # 准备数据
            data = {
                'ASIN': [],
                'URL': [],
                '标题': [],
                '品牌': [],
                '品牌类型': [],
                '尺寸': [],
                '重量': [],
                '评分': [],
                '评论数': [],
                '主类目排名': [],
                '主类目名称': [],
                '子类目排名': [],
                '子类目名称': [],
                '主图URL': [],
                '主ASIN': []
            }
            
            # 整理数据
            for result in self.visit_the_brand_asins:
                # 处理类目信息
                main_category_rank = None
                main_category_name = ""
                sub_category_rank = None
                sub_category_name = ""
                
                if result['sales_rank'] and len(result['sales_rank']) > 0:
                    main_category_rank = result['sales_rank'][0]['rank']
                    main_category_name = result['sales_rank'][0]['category']
                    
                if result['sales_rank'] and len(result['sales_rank']) > 1:
                    sub_category_rank = result['sales_rank'][1]['rank']
                    sub_category_name = result['sales_rank'][1]['category']
                
                # 添加数据
                data['ASIN'].append(result['asin'])
                data['URL'].append(result['url'])
                data['标题'].append(result['title'])
                data['品牌'].append(result['brand'])
                data['品牌类型'].append(result['brand_type'])
                data['尺寸'].append(result['dimensions'])
                data['重量'].append(result['weight'])
                data['评分'].append(result['rating'])
                data['评论数'].append(result['rating_count'])
                data['主类目排名'].append(main_category_rank)
                data['主类目名称'].append(main_category_name)
                data['子类目排名'].append(sub_category_rank)
                data['子类目名称'].append(sub_category_name)
                data['主图URL'].append(result['image_url'])
                data['主ASIN'].append(result['main_asin'])
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 保存到Excel
            output_file = "Visit_the_品牌产品.xlsx"
            self.save_to_excel_with_duplication_check(df, output_file, 'ASIN')
            
            print(f"已批量保存 {len(self.visit_the_brand_asins)} 个Visit the品牌ASIN到表格")
            
            # 清空缓存
            self.visit_the_brand_asins = []
                
        except Exception as e:
            print(f"批量保存Visit the品牌ASIN到表格时出错: {str(e)}")
    
    def save_other_brand_asins_batch(self):
        """批量保存其他品牌ASIN到Excel"""
        try:
            if not self.other_brand_asins:
                return
                
            # 准备数据
            data = {
                'ASIN': [],
                'URL': [],
                '标题': [],
                '品牌': [],
                '品牌类型': [],
                '尺寸': [],
                '重量': [],
                '评分': [],
                '评论数': [],
                '主类目排名': [],
                '主类目名称': [],
                '子类目排名': [],
                '子类目名称': [],
                '主图URL': [],
                '主ASIN': []
            }
            
            # 整理数据
            for result in self.other_brand_asins:
                # 处理类目信息
                main_category_rank = None
                main_category_name = ""
                sub_category_rank = None
                sub_category_name = ""
                
                if result['sales_rank'] and len(result['sales_rank']) > 0:
                    main_category_rank = result['sales_rank'][0]['rank']
                    main_category_name = result['sales_rank'][0]['category']
                    
                if result['sales_rank'] and len(result['sales_rank']) > 1:
                    sub_category_rank = result['sales_rank'][1]['rank']
                    sub_category_name = result['sales_rank'][1]['category']
                
                # 添加数据
                data['ASIN'].append(result['asin'])
                data['URL'].append(result['url'])
                data['标题'].append(result['title'])
                data['品牌'].append(result['brand'])
                data['品牌类型'].append(result['brand_type'])
                data['尺寸'].append(result['dimensions'])
                data['重量'].append(result['weight'])
                data['评分'].append(result['rating'])
                data['评论数'].append(result['rating_count'])
                data['主类目排名'].append(main_category_rank)
                data['主类目名称'].append(main_category_name)
                data['子类目排名'].append(sub_category_rank)
                data['子类目名称'].append(sub_category_name)
                data['主图URL'].append(result['image_url'])
                data['主ASIN'].append(result['main_asin'])
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 保存到Excel
            output_file = "Brand_品牌产品.xlsx"
            self.save_to_excel_with_duplication_check(df, output_file, 'ASIN')
            
            print(f"已批量保存 {len(self.other_brand_asins)} 个其他品牌ASIN到表格")
            
            # 清空缓存
            self.other_brand_asins = []
                
        except Exception as e:
            print(f"批量保存其他品牌ASIN到表格时出错: {str(e)}")
    
    def save_to_excel_with_duplication_check(self, df, output_file, key_column):
        """将DataFrame保存到Excel，检查并避免重复"""
        try:
            # 临时文件名，避免文件锁定问题
            temp_name = f"temp_{int(time.time())}.xlsx"
            
            if os.path.exists(output_file):
                try:
                    # 读取现有数据
                    existing_df = pd.read_excel(output_file)
                    
                    # 检查重复并排除
                    if key_column in existing_df.columns:
                        existing_keys = set(existing_df[key_column].values)
                        df = df[~df[key_column].isin(existing_keys)]
                        
                        if df.empty:
                            print(f"所有数据已存在于 {output_file} 中，跳过保存")
                            return
                    
                    # 合并数据
                    combined_df = pd.concat([existing_df, df], ignore_index=True)
                    
                    # 保存到临时文件
                    combined_df.to_excel(temp_name, index=False)
                    
                    # 尝试替换原文件
                    retry_count = 0
                    while retry_count < 5:
                        try:
                            if os.path.exists(output_file):
                                os.remove(output_file)
                            os.rename(temp_name, output_file)
                            break
                        except Exception:
                            retry_count += 1
                            time.sleep(0.5)
                    
                    print(f"已更新 {output_file}，添加 {len(df)} 条新记录")
                    
                except Exception as e:
                    # 如果读取现有文件失败，直接写入新文件
                    print(f"读取现有文件出错: {e}，创建新文件")
                    df.to_excel(output_file, index=False)
            else:
                # 直接创建新文件
                df.to_excel(output_file, index=False)
                print(f"已创建新文件 {output_file}，包含 {len(df)} 条记录")
                
        except Exception as e:
            print(f"保存Excel出错: {str(e)}")
    
    def save_remaining_results(self):
        """保存所有剩余的缓存结果"""
        with self.lock:
            # 保存所有剩余的结果
            if self.timeout_asins:
                self.save_timeout_asins_batch()
            
            if self.non_unique_brand_asins:
                self.save_non_unique_brand_asins_batch()
            
            if self.non_compliant_asins:
                self.save_non_compliant_asins_batch()

            if self.failed_to_get_info_asins:
                self.save_failed_to_get_info_asins_batch()

            if self.visit_the_brand_asins:
                self.save_visit_the_brand_asins_batch()
            
            if self.other_brand_asins:
                self.save_other_brand_asins_batch()
            
            print("已保存所有剩余结果")

    def safe_request(self, method, url, **kwargs):
        """
        安全的请求函数，包含重试和延迟机制
        
        Args:
            method: 请求方法 ('get', 'post', 等)
            url: 请求URL
            **kwargs: 传递给requests的参数
        
        Returns:
            requests.Response 对象或者 None(如果所有重试都失败)
        """
        # 对于123Proxy，跳过随机延迟，因为每次请求都是不同IP
        if not self.use_123proxy:
            random_factor = 1 + random.uniform(-self.download_delay_randomize, self.download_delay_randomize)
            delay = self.download_delay * random_factor
            print(f"请求 {url} 前等待 {delay:.2f} 秒...")
            time.sleep(delay)
        
        # 设置默认超时
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 30
        
        # 选择代理
        if 'proxies' not in kwargs and self.proxies:
            proxy = random.choice(self.proxies)
            kwargs['proxies'] = proxy
            print(f"使用代理: {self.get_proxy_info(proxy)}")
        
        # 每次请求使用新的随机Headers
        if 'headers' not in kwargs:
            kwargs['headers'] = self.Headers()
        
        # 执行请求并重试
        for attempt in range(self.max_retries + 1):  # +1 因为第一次不是重试
            if self.stop_event.is_set():
                return None  # 如果收到停止信号，终止请求
            
            try:
                start_time = time.time()
                if method.lower() == 'get':
                    response = self.session.get(url, **kwargs)
                elif method.lower() == 'post':
                    response = self.session.post(url, **kwargs)
                else:
                    raise ValueError(f"不支持的请求方法: {method}")
                
                elapsed = time.time() - start_time
                print(f"请求完成, 状态码: {response.status_code}, 用时: {elapsed:.2f}秒")
                
                # 检查状态码
                if response.status_code == 200:
                    return response
                elif response.status_code in self.retry_codes:
                    if attempt < self.max_retries:
                        backoff_time = self.calculate_backoff_time(attempt)
                        print(f"遇到状态码 {response.status_code}，第 {attempt+1} 次重试，等待 {backoff_time} 秒...")
                        time.sleep(backoff_time)
                        
                        # 如果是使用了代理，且返回了429(Too Many Requests)，则更换代理
                        if response.status_code == 429 and 'proxies' in kwargs:
                            self.handle_proxy_failure(kwargs['proxies'])
                            if self.proxies:  # 如果还有其他代理可用
                                new_proxy = random.choice(self.proxies)
                                kwargs['proxies'] = new_proxy
                                print(f"收到429状态码，更换代理为: {self.get_proxy_info(new_proxy)}")
                        
                        continue
                    else:
                        print(f"达到最大重试次数，请求失败: {url}")
                        if 'proxies' in kwargs:
                            self.handle_proxy_failure(kwargs['proxies'])
                        return response  # 返回最后一次尝试的响应
                else:
                    # 其他非重试状态码(如301, 302, 404等)，直接返回
                    return response
                    
            except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
                if attempt < self.max_retries:
                    backoff_time = self.calculate_backoff_time(attempt)
                    print(f"请求异常: {str(e)}, 第 {attempt+1} 次重试，等待 {backoff_time} 秒...")
                    time.sleep(backoff_time)
                    
                    # 超时或连接错误，可能是代理问题，尝试更换代理
                    if 'proxies' in kwargs:
                        self.handle_proxy_failure(kwargs['proxies'])
                        if self.proxies:  # 如果还有其他代理可用
                            new_proxy = random.choice(self.proxies)
                            kwargs['proxies'] = new_proxy
                            print(f"连接失败，更换代理为: {self.get_proxy_info(new_proxy)}")
                    
                    continue
                else:
                    print(f"达到最大重试次数，请求异常: {url} - {str(e)}")
                    if 'proxies' in kwargs:
                        if self.use_123proxy:
                            print("123Proxy请求异常，不移除代理")
                        else:
                            self.handle_proxy_failure(kwargs['proxies'], permanent=True)
                    return None  # 所有重试都失败
            except Exception as e:
                print(f"请求过程中发生未知异常: {str(e)}")
                if 'proxies' in kwargs:
                    self.handle_proxy_failure(kwargs['proxies'])
                return None
        
        # 所有重试都用完了，但仍然没有返回，可能是一个逻辑错误
        print(f"请求失败，所有重试尝试都已耗尽: {url}")
        return None

    def update_cookies_with_requests(self):
        """
        使用纯请求方式更新Amazon cookies，无需使用Selenium
        适用于获取基础cookie，速度更快，资源消耗更少
        """
        print("使用纯请求方式获取Amazon cookies...")
        
        # 保存原始session，稍后恢复
        original_session = self.session
        
        try:
            # 创建临时会话
            self.session = requests.Session()
            self.session.cookies.clear()
            
            # 第一步：访问亚马逊主页，获取初始cookie
            url = "https://www.amazon.com/?language=en_US"
            print(f"访问亚马逊主页: {url}")
            
            # 使用safe_request而不是直接的请求，确保使用SOCKS5代理
            response = self.safe_request('get', url, timeout=15)
            
            if not response or response.status_code != 200:
                print(f"访问亚马逊主页失败: {response.status_code if response else 'No response'}")
                return False
                
            # 检查是否遇到验证码
            if "Type the characters you see in this image" in response.text or "captcha" in response.text.lower():
                print("遇到验证码页面，需要使用Selenium方法")
                # 恢复原始session
                self.session = original_session
                return self.update_cookies_with_selenium()  # 回退到Selenium方法
                
            # 获取csrfToken和sessionID
            csrf_token = None
            for cookie in self.session.cookies:
                if cookie.name == 'csm-hit':
                    csrf_parts = cookie.value.split('&')
                    for part in csrf_parts:
                        if part.startswith('t='):
                            csrf_token = part[2:]
                            break
                    break
            
            if not csrf_token:
                print("未能获取CSRF令牌，尝试从页面内容中提取")
                # 尝试从页面提取
                csrf_match = re.search(r'csrf-token.*?value=["\'](.*?)["\']', response.text)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
                else:
                    print("无法获取CSRF令牌，尝试不使用令牌")
                    csrf_token = ""
            
            # 第三步：访问其他必要页面以获取完整cookie集
            urls = [
                "https://www.amazon.com/gp/yourstore",
                "https://www.amazon.com/gp/customer-preferences/select-language"
            ]
            
            for url in urls:
                try:
                    print(f"访问页面: {url}")
                    # 使用safe_request确保使用代理和延迟
                    response = self.safe_request('get', url, timeout=10)
                    if not response or response.status_code != 200:
                        print(f"访问页面 {url} 失败: {response.status_code if response else 'No response'}")
                except Exception as e:
                    print(f"访问页面 {url} 出错: {str(e)}")
            
            # 第四步：设置语言首选项
            try:
                lang_url = "https://www.amazon.com/gp/customer-preferences/save-settings"
                lang_payload = {
                    "ie": "UTF-8",
                    "preferenceName": "language",
                    "preferenceValue": "en_US"
                }
                
                if csrf_token:
                    lang_payload["csrfToken"] = csrf_token
                
                print("设置语言首选项为英语")
                headers = self.Headers()
                headers.update({
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Origin': 'https://www.amazon.com',
                    'Referer': 'https://www.amazon.com/gp/customer-preferences/select-language'
                })
                
                self.safe_request('post', lang_url, headers=headers, data=lang_payload, timeout=10)
            except Exception as e:
                print(f"设置语言首选项出错: {str(e)}")
            
            # 获取最终的cookies
            new_cookies = {}
            for cookie in self.session.cookies:
                new_cookies[cookie.name] = cookie.value
            
            # 检查关键cookie是否存在
            required_cookies = ['session-id', 'ubid-main']
            missing_cookies = [c for c in required_cookies if c not in new_cookies]
            
            if missing_cookies:
                print(f"警告: 缺少关键cookie: {missing_cookies}")
                print("尝试使用Selenium方法作为备选")
                # 恢复原始session
                self.session = original_session
                return self.update_cookies_with_selenium()
            
            # 添加语言相关cookies
            new_cookies['i18n-prefs'] = 'USD'
            new_cookies['lc-main'] = 'en_US'
            
            # 恢复原始会话并更新cookies
            self.session = original_session
            self.session.cookies.clear()
            
            for name, value in new_cookies.items():
                self.session.cookies.set(name, value, domain='.amazon.com')
            
            print(f"成功通过请求方式获取cookies: {len(new_cookies)} 个")
            return True
            
        except Exception as e:
            print(f"获取cookies过程中出错: {str(e)}")
            print("尝试回退到Selenium方法")
            # 恢复原始session
            self.session = original_session
            return self.update_cookies_with_selenium()

    def update_us_cookies(self):
        """更新访问亚马逊US站的cookies信息"""
        print("更新美国地区cookies...")
        
        # 首先尝试使用请求方式获取cookies
        if self.update_cookies_with_requests():
            print("使用请求方式更新cookies成功")
            
            # 测试cookies是否有效并保存到文件
            if self.test_cookies():
                self.save_cookies_to_file()
                return True
                
        # 请求方式失败，尝试使用Selenium获取新cookies
        if self.update_cookies_with_selenium():
            print("使用Selenium更新cookies成功")
            
            # 测试cookies是否有效并保存到文件
            if self.test_cookies():
                self.save_cookies_to_file()
                return True
        
        print("更新cookies失败")
        return False

    def force_english_page(self, response):
        """
        检测页面语言并强制转换为英文页面
        
        :param response: 请求响应对象
        :return: 如果成功转换为英文，则返回新的响应对象，否则返回None
        """
        soup = BeautifulSoup(response.content, 'html.parser')
        title = soup.find('title')
        
        # 检查页面是否为中文
        def contains_chinese(text):
            chinese_range = re.compile(u'[\u4e00-\u9fff]')
            return bool(chinese_range.search(text))
        
        if title and contains_chinese(title.text):
            print("检测到中文页面，尝试强制转换为英文...")
            
            # 1. 尝试直接修改URL参数
            url_parts = list(urllib.parse.urlparse(response.url))
            query = dict(urllib.parse.parse_qsl(url_parts[4]))
            query.update({
                'language': 'en_US',
                'country': 'US',
                'lc': 'en_US',
                'ie': 'UTF8'
            })
            url_parts[4] = urllib.parse.urlencode(query)
            new_url = urllib.parse.urlunparse(url_parts)
            
            # 2. 设置完整的英文Cookie
            self.session.cookies.set('i18n-prefs', 'USD', domain='.amazon.com', path='/')
            self.session.cookies.set('lc-main', 'en_US', domain='.amazon.com', path='/')
            self.session.cookies.set('session-id-time', str(int(time.time())), domain='.amazon.com', path='/')
            self.session.cookies.set('amazon-shopping-pref', 'language=en_US&country=US', domain='.amazon.com', path='/')
            
            # 3. 使用强化的请求头
            headers = self.Headers()
            
            # 4. 发起新的请求
            try:
                new_response = self.session.get(new_url, headers=headers, timeout=10)
                new_soup = BeautifulSoup(new_response.content, 'html.parser')
                new_title = new_soup.find('title')
                
                if new_title and not contains_chinese(new_title.text):
                    print("成功将页面转换为英文")
                    return new_response
                else:
                    print("直接转换失败，尝试通过Selenium更新Cookie...")
                    if self.update_cookies_with_selenium():
                        try:
                            final_response = self.session.get(new_url, headers=headers, timeout=10)
                            final_soup = BeautifulSoup(final_response.content, 'html.parser')
                            final_title = final_soup.find('title')
                            
                            if final_title and not contains_chinese(final_title.text):
                                print("使用Selenium更新Cookie后成功转换为英文")
                                return final_response
                        except Exception as e:
                            print(f"最终请求失败: {e}")
            except Exception as e:
                print(f"尝试强制英文页面失败: {e}")
                
        return None

def main():
    # 确保安装了必要的包
    try:
        import amazoncaptcha
    except ImportError:
        print("正在安装 amazoncaptcha 库...")
        import subprocess
        subprocess.check_call(["pip", "install", "amazoncaptcha"])
        from amazoncaptcha import AmazonCaptcha
        print("amazoncaptcha 安装成功")
    
    try:
        from selenium import webdriver
    except ImportError:
        print("正在安装 selenium 库...")
        import subprocess
        subprocess.check_call(["pip", "install", "selenium"])
        from selenium import webdriver
        print("selenium 安装成功")
    
    try:
        import pandas as pd
    except ImportError:
        print("正在安装 pandas 库...")
        import subprocess
        subprocess.check_call(["pip", "install", "pandas"])
        import pandas as pd
        print("pandas 安装成功")
    
    try:
        from tqdm import tqdm
    except ImportError:
        print("正在安装 tqdm 库...")
        import subprocess
        subprocess.check_call(["pip", "install", "tqdm"])
        from tqdm import tqdm
        print("tqdm 安装成功")
    
    try:
        import pickle
    except ImportError:
        print("正在安装 pickle 库...")
        import subprocess
        subprocess.check_call(["pip", "install", "pickle-mixin"])
        import pickle
        print("pickle 安装成功")
    
    # 显示智能代理选择系统信息
    print("\n" + "="*60)
    print("智能代理选择系统已启用")
    print("="*60)
    smart_proxy_manager.print_stats()
    print("="*60)

    # 创建GUI应用程序
    create_gui()

def create_gui():
    """创建图形用户界面"""
    root = tk.Tk()
    root.title("亚马逊产品筛选工具")
    root.geometry("600x480")  # 增加高度以容纳新组件
    
    # 设置样式
    style = ttk.Style()
    style.configure("TButton", font=("微软雅黑", 12), padding=10)
    style.configure("TLabel", font=("微软雅黑", 12))
    style.configure("TFrame", padding=10)
    
    # 创建主框架
    main_frame = ttk.Frame(root, style="TFrame")
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # 创建标题标签
    title_label = ttk.Label(main_frame, text="亚马逊产品筛选工具", font=("微软雅黑", 16, "bold"))
    title_label.pack(pady=20)
    
    # 创建按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.BOTH, expand=True)
    
    # 添加品牌搜索框架
    brand_search_frame = ttk.Frame(main_frame)
    brand_search_frame.pack(fill=tk.X, pady=10)
    
    brand_search_label = ttk.Label(brand_search_frame, text="品牌名称:")
    brand_search_label.pack(side=tk.LEFT, padx=5)
    
    brand_var = tk.StringVar()
    brand_entry = ttk.Entry(brand_search_frame, textvariable=brand_var, width=20)
    brand_entry.pack(side=tk.LEFT, padx=5)
    
    # 品牌搜索按钮
    def start_brand_search():
        brand_name = brand_var.get().strip()
        if not brand_name:
            messagebox.showwarning("警告", "请输入品牌名称")
            return
            
        try:
            num_threads = int(threads_var.get())
            max_retries = int(retries_var.get())
            status_var.set(f"开始搜索品牌: {brand_name}...")
            root.update()

            # 创建后台线程执行搜索
            def search_thread():
                scraper = AmazonProductScraper()
                scraper.set_max_retries(max_retries)  # 设置重试次数
                scraper.process_brand_search(brand_name)
                status_var.set(f"品牌 {brand_name} 搜索完成!")
                root.update()

            threading.Thread(target=search_thread, daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"品牌搜索过程中出错: {str(e)}")
            status_var.set("品牌搜索失败")
    
    brand_search_button = ttk.Button(brand_search_frame, text="搜索品牌", command=start_brand_search)
    brand_search_button.pack(side=tk.LEFT, padx=5)
    
    # 配置框架
    config_frame = ttk.Frame(main_frame)
    config_frame.pack(fill=tk.X, pady=10)

    # 线程数量框架
    threads_frame = ttk.Frame(config_frame)
    threads_frame.pack(side=tk.LEFT, padx=10)

    # 重试次数框架
    retries_frame = ttk.Frame(config_frame)
    retries_frame.pack(side=tk.LEFT, padx=10)
    
    threads_label = ttk.Label(threads_frame, text="线程数量:")
    threads_label.pack(side=tk.LEFT, padx=5)

    threads_var = tk.StringVar(value="5")
    threads_entry = ttk.Entry(threads_frame, textvariable=threads_var, width=5)
    threads_entry.pack(side=tk.LEFT, padx=5)

    # 重试次数配置
    retries_label = ttk.Label(retries_frame, text="重试次数:")
    retries_label.pack(side=tk.LEFT, padx=5)

    retries_var = tk.StringVar(value="5")
    retries_entry = ttk.Entry(retries_frame, textvariable=retries_var, width=5)
    retries_entry.pack(side=tk.LEFT, padx=5)
    
    # 状态框架
    status_frame = ttk.Frame(main_frame)
    status_frame.pack(fill=tk.X, pady=10)
    
    status_var = tk.StringVar(value="准备就绪")
    status_label = ttk.Label(status_frame, textvariable=status_var)
    status_label.pack(fill=tk.X)
    
    # 采集按钮
    def start_collection():
        try:
            num_threads = int(threads_var.get())
            max_retries = int(retries_var.get())
            status_var.set("开始采集商品信息...")
            root.update()

            # 创建后台线程执行采集
            def collection_thread():
                scraper = AmazonProductScraper()
                scraper.set_max_retries(max_retries)  # 设置重试次数
                scraper.process_asins_with_threads(num_threads)
                status_var.set("采集完成!")
                root.update()

            threading.Thread(target=collection_thread, daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"采集过程中出错: {str(e)}")
            status_var.set("采集失败")
    
    collect_button = ttk.Button(button_frame, text="采集商品信息", command=start_collection)
    collect_button.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
    
    # 筛品牌按钮 - 修改名称，明确是获取品牌信息
    def start_brand_filtering():
        try:
            num_threads = int(threads_var.get())
            status_var.set("开始获取品牌信息...")
            root.update()
            
            # 创建后台线程执行筛选
            def brand_thread():
                scraper = AmazonProductScraper()
                scraper.process_asins_with_threads(num_threads)
                status_var.set("品牌信息获取完成!")
                root.update()
            
            threading.Thread(target=brand_thread, daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"获取品牌信息过程中出错: {str(e)}")
            status_var.set("获取品牌信息失败")
    
    brand_button = ttk.Button(button_frame, text="获取品牌信息", command=start_brand_filtering)
    brand_button.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
    
    # 筛专利按钮
    def start_patent_filtering():
        try:
            num_threads = int(threads_var.get())
            status_var.set("开始筛选专利...")
            root.update()
            
            # 创建后台线程执行筛选
            def patent_thread():
                scraper = AmazonProductScraper()
                scraper.process_patent_filtering(num_threads)
                status_var.set("专利筛选完成!")
                root.update()
            
            threading.Thread(target=patent_thread, daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"专利筛选过程中出错: {str(e)}")
            status_var.set("专利筛选失败")
    
    patent_button = ttk.Button(button_frame, text="筛选专利", command=start_patent_filtering)
    patent_button.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
    
    # 设置网格权重以均匀分布
    button_frame.columnconfigure(0, weight=1)
    button_frame.columnconfigure(1, weight=1)
    button_frame.rowconfigure(0, weight=1)
    button_frame.rowconfigure(1, weight=1)
    
    # 运行主循环
    root.mainloop()

if __name__ == "__main__":
    main()
