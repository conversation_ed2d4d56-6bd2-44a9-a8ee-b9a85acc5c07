#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的代理账户信息是否正常工作
"""

import requests
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def test_single_proxy(proxy_info):
    """测试单个代理服务器"""
    name, port = proxy_info
    
    proxy_url = f"http://u1856561711690016:<EMAIL>:{port}"
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    print(f"正在测试 {name} (端口:{port})...")
    
    try:
        start_time = time.time()
        response = requests.get(
            'https://ifconfig.me', 
            proxies=proxies, 
            timeout=15,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            # 尝试提取IP地址
            try:
                if len(response.text.strip()) < 100:  # 简单检查是否是IP
                    ip = response.text.strip()
                else:
                    # 如果返回HTML，尝试从中提取IP
                    import re
                    ip_match = re.search(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', response.text)
                    ip = ip_match.group() if ip_match else "HTML页面"
            except:
                ip = "无法解析"
            
            return {
                'name': name,
                'port': port,
                'status': 'success',
                'response_time': response_time,
                'ip': ip
            }
        else:
            return {
                'name': name,
                'port': port,
                'status': 'http_error',
                'error': f"HTTP {response.status_code}"
            }
            
    except requests.exceptions.ProxyError as e:
        return {
            'name': name,
            'port': port,
            'status': 'proxy_error',
            'error': '代理认证失败或连接错误'
        }
    except requests.exceptions.Timeout as e:
        return {
            'name': name,
            'port': port,
            'status': 'timeout',
            'error': '请求超时'
        }
    except requests.exceptions.ConnectionError as e:
        return {
            'name': name,
            'port': port,
            'status': 'connection_error',
            'error': '连接失败'
        }
    except Exception as e:
        return {
            'name': name,
            'port': port,
            'status': 'unknown_error',
            'error': str(e)
        }

def main():
    print("测试新的123Proxy账户信息")
    print("="*50)
    print("新账户: u1856561711690016")
    print("新密码: F6DYEJanBYcQ")
    print("="*50)
    
    # 定义所有代理服务器
    proxy_servers = [
        ("服务器1", 36931),
        ("服务器2", 36930),
        ("服务器3", 36927),
        ("服务器4", 36928),
        ("服务器5", 36929),
    ]
    
    print(f"开始测试 {len(proxy_servers)} 个代理服务器...")
    print("-" * 50)
    
    # 并发测试所有代理
    results = []
    with ThreadPoolExecutor(max_workers=3) as executor:
        future_to_proxy = {executor.submit(test_single_proxy, proxy): proxy for proxy in proxy_servers}
        
        for future in as_completed(future_to_proxy):
            proxy = future_to_proxy[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f'代理 {proxy[0]} 测试时发生异常: {exc}')
    
    # 按原始顺序排序结果
    results.sort(key=lambda x: [p[0] for p in proxy_servers].index(x['name']))
    
    # 显示测试结果
    print("\n" + "="*60)
    print("测试结果")
    print("="*60)
    
    working_proxies = []
    failed_proxies = []
    
    for result in results:
        status_icon = {
            'success': '✅',
            'proxy_error': '❌',
            'timeout': '⏰',
            'connection_error': '🔌',
            'http_error': '⚠️',
            'unknown_error': '❓'
        }.get(result['status'], '❓')
        
        print(f"\n{status_icon} {result['name']} (端口:{result['port']})")
        
        if result['status'] == 'success':
            print(f"   ✅ 状态: 正常工作")
            print(f"   ⏱️ 响应时间: {result['response_time']:.2f}秒")
            print(f"   🌐 代理IP: {result['ip']}")
            working_proxies.append(result)
        else:
            print(f"   ❌ 状态: {result['status']}")
            print(f"   ⚠️ 错误: {result.get('error', '未知错误')}")
            failed_proxies.append(result)
    
    # 总结
    print("\n" + "="*60)
    print("总结")
    print("="*60)
    print(f"✅ 可用代理: {len(working_proxies)}/{len(proxy_servers)}")
    print(f"❌ 不可用代理: {len(failed_proxies)}/{len(proxy_servers)}")
    
    if working_proxies:
        print(f"\n🎉 可用的代理服务器:")
        for proxy in working_proxies:
            print(f"  - {proxy['name']}: 端口{proxy['port']} (响应时间: {proxy['response_time']:.2f}s)")
    
    if failed_proxies:
        print(f"\n⚠️ 需要检查的代理:")
        for proxy in failed_proxies:
            print(f"  - {proxy['name']}: {proxy['status']} - {proxy.get('error', '未知错误')}")
    
    # 建议
    print(f"\n💡 建议:")
    if len(working_proxies) == len(proxy_servers):
        print("🎉 所有代理都正常工作！新账户配置成功")
        print("✅ 可以正常使用品牌.py进行数据抓取")
    elif len(working_proxies) > 0:
        print(f"⚠️ {len(working_proxies)} 个代理正常工作，可以继续使用")
        print("💡 建议检查失败的代理服务器配置")
    else:
        print("❌ 所有代理都不可用，请检查:")
        print("   1. 新账户信息是否正确")
        print("   2. 账户是否有剩余流量")
        print("   3. 网络连接是否正常")

if __name__ == "__main__":
    main()
