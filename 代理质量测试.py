import requests
import threading
import time
import json
from collections import defaultdict

class ProxyTester:
    def __init__(self):
        # 定义所有代理服务器
        self.proxies = [
            {
                "name": "服务器1",
                "http": "http://u1856561711670614:P0BI4<PERSON>un<PERSON>ep<PERSON>@proxy.123proxy.cn:36931",
                "https": "http://u1856561711670614:<EMAIL>:36931"
            },
            {
                "name": "服务器2", 
                "http": "http://u1856561711670614:<EMAIL>:36930",
                "https": "http://u1856561711670614:<EMAIL>:36930"
            },
            {
                "name": "服务器3",
                "http": "http://u1856561711670614:P0BI4<PERSON>un<PERSON>ep<PERSON>@proxy.123proxy.cn:36927",
                "https": "http://u1856561711670614:P0BI4<PERSON><EMAIL>:36927"
            },
            {
                "name": "服务器4",
                "http": "http://u1856561711670614:<EMAIL>:36928",
                "https": "http://u1856561711670614:<EMAIL>:36928"
            },
            {
                "name": "服务器5",
                "http": "http://u1856561711670614:<EMAIL>:36929",
                "https": "http://u1856561711670614:<EMAIL>:36929"
            }
        ]
        
        # 统计数据
        self.stats = defaultdict(lambda: {"success": 0, "failure": 0, "total": 0, "avg_response_time": 0})
        self.stats_lock = threading.Lock()
        
    def test_proxy(self, proxy, test_count=10):
        """测试单个代理的质量"""
        print(f"开始测试 {proxy['name']}...")
        
        proxy_settings = {
            "http": proxy["http"],
            "https": proxy["https"]
        }
        
        success_count = 0
        total_response_time = 0
        
        for i in range(test_count):
            try:
                start_time = time.time()
                response = requests.get("https://ifconfig.me", proxies=proxy_settings, timeout=10)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    success_count += 1
                    total_response_time += response_time
                    print(f"  {proxy['name']} 测试 {i+1}/{test_count}: 成功 ({response_time:.2f}s) - IP: {response.text.strip()}")
                else:
                    print(f"  {proxy['name']} 测试 {i+1}/{test_count}: HTTP错误 {response.status_code}")
                    
            except Exception as e:
                print(f"  {proxy['name']} 测试 {i+1}/{test_count}: 失败 - {str(e)}")
            
            time.sleep(1)  # 避免请求过快
        
        # 更新统计数据
        with self.stats_lock:
            self.stats[proxy['name']]["success"] = success_count
            self.stats[proxy['name']]["failure"] = test_count - success_count
            self.stats[proxy['name']]["total"] = test_count
            if success_count > 0:
                self.stats[proxy['name']]["avg_response_time"] = total_response_time / success_count
        
        success_rate = success_count / test_count
        avg_time = total_response_time / success_count if success_count > 0 else 0
        print(f"{proxy['name']} 测试完成: 成功率 {success_rate:.1%}, 平均响应时间 {avg_time:.2f}s\n")
        
        return success_rate, avg_time
    
    def test_all_proxies(self, test_count=10):
        """测试所有代理"""
        print("=== 开始代理质量测试 ===\n")
        
        threads = []
        for proxy in self.proxies:
            thread = threading.Thread(target=self.test_proxy, args=(proxy, test_count))
            thread.start()
            threads.append(thread)
        
        # 等待所有测试完成
        for thread in threads:
            thread.join()
        
        self.print_final_report()
    
    def print_final_report(self):
        """打印最终测试报告"""
        print("\n" + "="*80)
        print("代理质量测试报告")
        print("="*80)
        print(f"{'代理名称':<12} {'成功率':<10} {'平均响应时间':<12} {'质量评分':<10} {'推荐等级':<10}")
        print("-" * 80)
        
        # 计算质量评分并排序
        proxy_scores = []
        for proxy in self.proxies:
            name = proxy['name']
            stats = self.stats[name]
            
            if stats['total'] > 0:
                success_rate = stats['success'] / stats['total']
                avg_time = stats['avg_response_time']
                
                # 质量评分算法：成功率权重70%，响应时间权重30%
                # 响应时间评分：5秒以内满分，超过5秒递减
                time_score = max(0, 1 - (avg_time - 1) / 4) if avg_time > 0 else 0
                quality_score = success_rate * 0.7 + time_score * 0.3
                
                # 推荐等级
                if quality_score >= 0.8:
                    level = "优秀"
                elif quality_score >= 0.6:
                    level = "良好"
                elif quality_score >= 0.4:
                    level = "一般"
                else:
                    level = "较差"
                
                proxy_scores.append({
                    'name': name,
                    'success_rate': success_rate,
                    'avg_time': avg_time,
                    'quality_score': quality_score,
                    'level': level
                })
        
        # 按质量评分排序
        proxy_scores.sort(key=lambda x: x['quality_score'], reverse=True)
        
        for proxy in proxy_scores:
            print(f"{proxy['name']:<12} {proxy['success_rate']:.1%}<10 {proxy['avg_time']:.2f}s<12 {proxy['quality_score']:.2f}<10 {proxy['level']:<10}")
        
        print("-" * 80)
        
        # 保存测试结果
        self.save_test_results(proxy_scores)
        
        # 给出使用建议
        print("\n使用建议:")
        if proxy_scores:
            best_proxy = proxy_scores[0]
            print(f"1. 推荐优先使用: {best_proxy['name']} (质量评分: {best_proxy['quality_score']:.2f})")
            
            good_proxies = [p for p in proxy_scores if p['quality_score'] >= 0.6]
            if len(good_proxies) > 1:
                print(f"2. 备用选择: {', '.join([p['name'] for p in good_proxies[1:]])}")
            
            poor_proxies = [p for p in proxy_scores if p['quality_score'] < 0.4]
            if poor_proxies:
                print(f"3. 建议避免使用: {', '.join([p['name'] for p in poor_proxies])}")
    
    def save_test_results(self, proxy_scores):
        """保存测试结果到文件"""
        try:
            result = {
                "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "proxy_scores": proxy_scores,
                "raw_stats": dict(self.stats)
            }
            
            with open("proxy_test_results.json", "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"\n测试结果已保存到: proxy_test_results.json")
            
        except Exception as e:
            print(f"保存测试结果失败: {e}")

def main():
    tester = ProxyTester()
    
    print("代理质量测试工具")
    print("这将测试所有5个代理服务器的质量")
    
    test_count = input("请输入每个代理的测试次数 (默认10次): ").strip()
    if not test_count:
        test_count = 10
    else:
        try:
            test_count = int(test_count)
        except:
            test_count = 10
    
    print(f"\n将对每个代理进行 {test_count} 次测试...\n")
    
    tester.test_all_proxies(test_count)

if __name__ == "__main__":
    main()
