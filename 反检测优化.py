#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反检测优化脚本 - 测试不同的反检测策略
"""

import requests
import time
import random
import json

def get_optimized_headers():
    """生成优化的请求头，减少被检测的可能性"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ]
    
    return {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
        'DNT': '1',
        'Pragma': 'no-cache'
    }

def test_amazon_access(proxy_info, strategy_name, headers, delay_range):
    """测试Amazon访问策略"""
    name, port = proxy_info
    
    proxy_url = f"http://u1856561711690016:<EMAIL>:{port}"
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    print(f"\n🧪 测试策略: {strategy_name}")
    print(f"🔧 使用代理: {name} (端口:{port})")
    print(f"⏱️ 延迟范围: {delay_range[0]}-{delay_range[1]}秒")
    
    # 测试不同的Amazon页面
    test_urls = [
        ('产品页面', 'https://www.amazon.com/dp/B08N5WRWNW'),
        ('搜索页面', 'https://www.amazon.com/s?k=wireless+headphones'),
        ('分类页面', 'https://www.amazon.com/Electronics/b?node=172282')
    ]
    
    results = []
    
    for test_name, test_url in test_urls:
        # 随机延迟
        delay = random.uniform(delay_range[0], delay_range[1])
        print(f"   ⏳ 等待 {delay:.1f}秒...")
        time.sleep(delay)
        
        try:
            print(f"   🌐 访问 {test_name}: {test_url}")
            
            start_time = time.time()
            response = requests.get(
                test_url,
                proxies=proxies,
                headers=headers,
                timeout=25,
                allow_redirects=True
            )
            response_time = time.time() - start_time
            
            # 分析响应
            status = 'unknown'
            if response.status_code == 200:
                content = response.text.lower()
                if 'captcha' in content or 'robot' in content:
                    status = 'blocked'
                    print(f"   ❌ 被检测为机器人/验证码")
                elif 'productTitle' in response.text or 'product-title' in response.text:
                    status = 'success'
                    print(f"   ✅ 成功访问产品页面")
                elif 's-result-item' in response.text:
                    status = 'success'
                    print(f"   ✅ 成功访问搜索结果")
                elif len(response.text) > 10000:
                    status = 'success'
                    print(f"   ✅ 成功访问页面")
                else:
                    status = 'limited'
                    print(f"   ⚠️ 页面内容受限")
            elif response.status_code == 503:
                status = 'rate_limited'
                print(f"   ⚠️ 服务暂时不可用 (503)")
            else:
                status = 'error'
                print(f"   ❌ HTTP错误: {response.status_code}")
            
            results.append({
                'test': test_name,
                'url': test_url,
                'status': status,
                'status_code': response.status_code,
                'response_time': response_time,
                'content_length': len(response.text)
            })
            
            print(f"   📊 状态码: {response.status_code}, 响应时间: {response_time:.2f}s, 内容长度: {len(response.text)}")
            
        except Exception as e:
            print(f"   ❌ 请求失败: {str(e)}")
            results.append({
                'test': test_name,
                'url': test_url,
                'status': 'error',
                'error': str(e)
            })
    
    return results

def analyze_strategy_results(strategy_name, results):
    """分析策略结果"""
    print(f"\n📊 策略 '{strategy_name}' 结果分析:")
    
    success_count = sum(1 for r in results if r.get('status') == 'success')
    blocked_count = sum(1 for r in results if r.get('status') == 'blocked')
    limited_count = sum(1 for r in results if r.get('status') == 'limited')
    error_count = sum(1 for r in results if r.get('status') == 'error')
    rate_limited_count = sum(1 for r in results if r.get('status') == 'rate_limited')
    
    total = len(results)
    
    print(f"   ✅ 成功: {success_count}/{total} ({success_count/total*100:.1f}%)")
    print(f"   ❌ 被阻止: {blocked_count}/{total} ({blocked_count/total*100:.1f}%)")
    print(f"   ⚠️ 受限: {limited_count}/{total} ({limited_count/total*100:.1f}%)")
    print(f"   🚫 限流: {rate_limited_count}/{total} ({rate_limited_count/total*100:.1f}%)")
    print(f"   💥 错误: {error_count}/{total} ({error_count/total*100:.1f}%)")
    
    # 计算策略评分
    score = (success_count * 10 + limited_count * 5 + rate_limited_count * 2) / total
    print(f"   🎯 策略评分: {score:.1f}/10")
    
    return score

def main():
    print("🔍 Amazon反检测策略测试")
    print("="*60)
    
    # 选择最快的代理进行测试
    proxy_info = ("服务器5", 36929)  # 根据之前测试，这是最快的
    
    # 定义不同的测试策略
    strategies = [
        {
            'name': '保守策略',
            'headers': get_optimized_headers(),
            'delay_range': (8, 15)  # 8-15秒延迟
        },
        {
            'name': '中等策略', 
            'headers': get_optimized_headers(),
            'delay_range': (5, 10)  # 5-10秒延迟
        },
        {
            'name': '激进策略',
            'headers': get_optimized_headers(), 
            'delay_range': (2, 5)   # 2-5秒延迟
        }
    ]
    
    strategy_scores = {}
    
    for strategy in strategies:
        print(f"\n{'='*60}")
        print(f"🧪 测试策略: {strategy['name']}")
        print(f"{'='*60}")
        
        results = test_amazon_access(
            proxy_info,
            strategy['name'],
            strategy['headers'],
            strategy['delay_range']
        )
        
        score = analyze_strategy_results(strategy['name'], results)
        strategy_scores[strategy['name']] = score
    
    # 最终建议
    print(f"\n{'='*60}")
    print("🎯 最终建议")
    print(f"{'='*60}")
    
    best_strategy = max(strategy_scores.items(), key=lambda x: x[1])
    print(f"🏆 最佳策略: {best_strategy[0]} (评分: {best_strategy[1]:.1f})")
    
    print(f"\n💡 建议的品牌.py优化设置:")
    if best_strategy[0] == '保守策略':
        print("   - 请求延迟: 8-15秒")
        print("   - 建议线程数: 1-2个")
        print("   - 适合长期稳定运行")
    elif best_strategy[0] == '中等策略':
        print("   - 请求延迟: 5-10秒") 
        print("   - 建议线程数: 2-3个")
        print("   - 平衡速度和稳定性")
    else:
        print("   - 请求延迟: 2-5秒")
        print("   - 建议线程数: 3-5个")
        print("   - 适合快速抓取，但风险较高")
    
    print(f"\n⚠️ 重要提醒:")
    print("   1. Amazon的反爬虫策略会动态调整")
    print("   2. 建议从保守策略开始，逐步调整")
    print("   3. 如果大量出现验证码，立即降低频率")
    print("   4. 考虑在不同时间段进行测试")

if __name__ == "__main__":
    main()
