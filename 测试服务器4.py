#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试服务器4是否正常工作
"""

import requests
import time

def test_server4():
    """测试服务器4的连接"""
    print("测试服务器4 (proxy.123proxy.cn:36928)")
    print("="*50)
    
    proxy_url = "http://u1856561711670614:<EMAIL>:36928"
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    test_urls = [
        'https://ifconfig.me',
        'https://httpbin.org/ip',
        'https://www.amazon.com/robots.txt'
    ]
    
    success_count = 0
    total_tests = len(test_urls)
    
    for i, test_url in enumerate(test_urls, 1):
        print(f"\n测试 {i}/{total_tests}: {test_url}")
        try:
            start_time = time.time()
            response = requests.get(
                test_url, 
                proxies=proxies, 
                timeout=20,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                success_count += 1
                print(f"✅ 成功 (响应时间: {response_time:.2f}秒)")
                
                # 显示IP信息
                if 'ifconfig.me' in test_url:
                    try:
                        ip = response.text.strip()
                        if len(ip) < 100:  # 简单检查是否是IP地址
                            print(f"   代理IP: {ip}")
                        else:
                            print("   返回了HTML页面而不是纯IP")
                    except:
                        print("   无法解析IP信息")
                elif 'httpbin.org' in test_url:
                    try:
                        ip_info = response.json()
                        print(f"   代理IP: {ip_info.get('origin', 'unknown')}")
                    except:
                        print("   无法解析JSON响应")
                elif 'amazon.com' in test_url:
                    print(f"   Amazon访问正常 (响应长度: {len(response.text)} 字符)")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.ProxyError as e:
            print(f"❌ 代理连接错误: {str(e)}")
        except requests.exceptions.Timeout as e:
            print(f"❌ 请求超时: {str(e)}")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {str(e)}")
        except Exception as e:
            print(f"❌ 未知错误: {str(e)}")
        
        # 短暂休息
        if i < total_tests:
            time.sleep(2)
    
    print(f"\n" + "="*50)
    print(f"测试结果: {success_count}/{total_tests} 成功")
    
    if success_count == total_tests:
        print("🎉 服务器4工作完全正常！")
    elif success_count > 0:
        print("⚠️ 服务器4部分正常，可能存在一些问题")
    else:
        print("❌ 服务器4完全不可用，请检查配置")
    
    return success_count > 0

if __name__ == "__main__":
    test_server4()
