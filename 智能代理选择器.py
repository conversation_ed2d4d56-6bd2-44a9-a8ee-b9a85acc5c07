import requests
import threading
import time
import random
import json
import os
from collections import defaultdict
from datetime import datetime

class SmartProxyManager:
    def __init__(self):
        # 定义所有代理服务器
        self.proxies = [
            {
                "name": "服务器1",
                "http": "http://u1856561711670614:P0BI4Uun<PERSON><EMAIL>:36931",
                "https": "http://u1856561711670614:<EMAIL>:36931"
            },
            {
                "name": "服务器2", 
                "http": "http://u1856561711670614:<EMAIL>:36930",
                "https": "http://u1856561711670614:<EMAIL>:36930"
            },
            {
                "name": "服务器3",
                "http": "http://u1856561711670614:P0BI4<PERSON><PERSON><PERSON><PERSON><PERSON>@proxy.123proxy.cn:36927",
                "https": "http://u1856561711670614:<EMAIL>:36927"
            },
            {
                "name": "服务器4",
                "http": "http://u1856561711670614:<EMAIL>:36928",
                "https": "http://u1856561711670614:<EMAIL>:36928"
            },
            {
                "name": "服务器5",
                "http": "http://u1856561711670614:<EMAIL>:36929",
                "https": "http://u1856561711670614:<EMAIL>:36929"
            }
        ]
        
        # 统计数据
        self.stats = defaultdict(lambda: {"success": 0, "failure": 0, "total": 0})
        self.stats_file = "proxy_stats.json"
        
        # 加载历史统计数据
        self.load_stats()
        
        # 线程锁，保护统计数据
        self.stats_lock = threading.Lock()
        
        # 最小尝试次数，避免新代理因为样本太少而被忽略
        self.min_attempts = 10
        
    def load_stats(self):
        """加载历史统计数据"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for name, stats in data.items():
                        self.stats[name] = stats
                print(f"已加载历史统计数据: {len(data)} 个代理")
        except Exception as e:
            print(f"加载统计数据失败: {e}")
    
    def save_stats(self):
        """保存统计数据到文件"""
        try:
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(dict(self.stats), f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存统计数据失败: {e}")
    
    def get_success_rate(self, proxy_name):
        """计算代理的成功率"""
        stats = self.stats[proxy_name]
        if stats["total"] == 0:
            return 0.5  # 新代理给予中等权重
        return stats["success"] / stats["total"]
    
    def get_proxy_weights(self):
        """计算每个代理的权重"""
        weights = []
        for proxy in self.proxies:
            name = proxy["name"]
            success_rate = self.get_success_rate(name)
            
            # 如果尝试次数太少，给予额外的探索权重
            if self.stats[name]["total"] < self.min_attempts:
                weight = 1.0  # 给新代理更多机会
            else:
                # 基于成功率计算权重，最低权重为0.1，避免完全排除某个代理
                weight = max(0.1, success_rate)
            
            weights.append(weight)
        
        return weights
    
    def select_proxy(self):
        """基于权重随机选择代理"""
        weights = self.get_proxy_weights()
        
        # 加权随机选择
        total_weight = sum(weights)
        if total_weight == 0:
            # 如果所有权重都是0，随机选择
            return random.choice(self.proxies)
        
        # 生成随机数
        rand = random.uniform(0, total_weight)
        
        # 找到对应的代理
        current_weight = 0
        for i, weight in enumerate(weights):
            current_weight += weight
            if rand <= current_weight:
                return self.proxies[i]
        
        # 备用方案
        return self.proxies[-1]
    
    def record_result(self, proxy_name, success):
        """记录代理使用结果"""
        with self.stats_lock:
            if success:
                self.stats[proxy_name]["success"] += 1
            else:
                self.stats[proxy_name]["failure"] += 1
            self.stats[proxy_name]["total"] += 1
    
    def print_stats(self):
        """打印统计信息"""
        print("\n=== 代理服务器统计信息 ===")
        print(f"{'代理名称':<10} {'成功次数':<8} {'失败次数':<8} {'总次数':<8} {'成功率':<8} {'权重':<8}")
        print("-" * 70)
        
        weights = self.get_proxy_weights()
        for i, proxy in enumerate(self.proxies):
            name = proxy["name"]
            stats = self.stats[name]
            success_rate = self.get_success_rate(name)
            weight = weights[i]
            
            print(f"{name:<10} {stats['success']:<8} {stats['failure']:<8} {stats['total']:<8} {success_rate:.2%}<8 {weight:.2f}<8")
        print("-" * 70)

class SmartProxyRequester:
    def __init__(self, proxy_manager, target_url="https://ifconfig.me"):
        self.proxy_manager = proxy_manager
        self.target_url = target_url
        self.session_alive = 15  # 会话存活时间
        
    def make_requests(self, thread_id):
        """执行请求的线程函数"""
        session = requests.Session()
        switch_time = time.time() + self.session_alive
        current_proxy = None
        
        while True:
            try:
                # 定期切换代理或首次选择代理
                if current_proxy is None or time.time() > switch_time:
                    current_proxy = self.proxy_manager.select_proxy()
                    switch_time = time.time() + self.session_alive
                    session.close()
                    session = requests.Session()
                    print(f"[线程{thread_id}] 选择代理: {current_proxy['name']}")
                
                # 设置代理
                proxy_settings = {
                    "http": current_proxy["http"],
                    "https": current_proxy["https"]
                }
                
                # 发送请求
                response = session.get(self.target_url, proxies=proxy_settings, timeout=10)
                
                if response.status_code == 200:
                    print(f"[线程{thread_id}] [{current_proxy['name']}] 成功: {response.text.strip()}")
                    self.proxy_manager.record_result(current_proxy["name"], True)
                else:
                    print(f"[线程{thread_id}] [{current_proxy['name']}] HTTP错误: {response.status_code}")
                    self.proxy_manager.record_result(current_proxy["name"], False)
                
                # 短暂休息
                time.sleep(1)
                
            except Exception as e:
                if current_proxy:
                    print(f"[线程{thread_id}] [{current_proxy['name']}] 请求失败: {str(e)}")
                    self.proxy_manager.record_result(current_proxy["name"], False)
                
                # 重新创建会话
                session.close()
                session = requests.Session()
                current_proxy = None  # 强制重新选择代理
                time.sleep(2)  # 失败后等待更长时间

def main():
    # 创建代理管理器
    proxy_manager = SmartProxyManager()
    
    # 创建请求器
    requester = SmartProxyRequester(proxy_manager)
    
    # 并发线程数
    num_threads = 5
    threads = []
    
    print("启动智能代理选择系统...")
    print(f"使用 {num_threads} 个线程进行并发请求")
    
    # 启动线程
    for i in range(num_threads):
        thread = threading.Thread(target=requester.make_requests, args=(i+1,))
        thread.daemon = True  # 设置为守护线程
        thread.start()
        threads.append(thread)
    
    try:
        # 定期打印统计信息
        while True:
            time.sleep(30)  # 每30秒打印一次统计
            proxy_manager.print_stats()
            proxy_manager.save_stats()  # 保存统计数据
            
    except KeyboardInterrupt:
        print("\n正在停止...")
        proxy_manager.save_stats()
        print("统计数据已保存")

if __name__ == "__main__":
    main()
